import dotenv from 'dotenv';
dotenv.config();

import { PrismaClient } from '@prisma/client';

async function createFreshJobs() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Finding images in PROCESSING status...\n');

    // Find all images with PROCESSING status
    const processingImages = await prisma.image.findMany({
      where: {
        processingStatus: 'PROCESSING'
      },
      select: {
        id: true,
        userId: true,
        filename: true,
        s3Key: true,
        s3Bucket: true,
        mode: true,
        createdAt: true
      }
    });

    console.log(`📸 Found ${processingImages.length} images in PROCESSING status:`);
    processingImages.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.filename} (${image.id})`);
    });

    if (processingImages.length === 0) {
      console.log('✅ No images in PROCESSING status found.');
      return;
    }

    // Import services after dotenv is loaded
    const { QueueService } = await import('../src/server/services/queue');
    const { RedisService } = await import('../src/server/services/redis');

    // Initialize services
    await RedisService.initialize();
    await QueueService.initialize('producer');

    console.log('\n🚀 Creating fresh queue jobs...\n');

    for (const image of processingImages) {
      try {
        console.log(`📋 Creating fresh job for: ${image.filename}`);

        // Create Bull queue job (using standard processing)
        const job = await QueueService.addImageProcessingJob({
          imageId: image.id,
          userId: image.userId,
          s3Key: image.s3Key,
          s3Bucket: image.s3Bucket,
          mode: image.mode,
          settings: {
            processingType: 'standard'
          }
        });

        if (job) {
          console.log(`  ✓ Fresh queue job created: ${job.id} for image ${image.id}`);
        } else {
          console.log(`  ❌ Failed to create queue job for image ${image.id}`);
        }

      } catch (error) {
        console.error(`  ❌ Failed to create job for image ${image.filename}:`, error);
      }
    }

    console.log('\n✅ Finished creating fresh jobs');

    // Wait a moment and check queue stats
    console.log('\n📊 Waiting 3 seconds and checking queue stats...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const stats = await QueueService.getQueueStats();
    console.log('📊 Updated Queue Statistics:');
    console.log(`  - Waiting: ${stats.waiting}`);
    console.log(`  - Active: ${stats.active}`);
    console.log(`  - Completed: ${stats.completed}`);
    console.log(`  - Failed: ${stats.failed}`);

  } catch (error) {
    console.error('❌ Error creating fresh jobs:', error);
  } finally {
    await prisma.$disconnect();
    process.exit(0);
  }
}

createFreshJobs();
