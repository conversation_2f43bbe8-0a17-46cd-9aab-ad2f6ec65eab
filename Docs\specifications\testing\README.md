# I2D-Convert Testing Guide

This directory contains comprehensive testing documentation and tools for the I2D-Convert application.

## Overview

The testing suite covers the complete end-to-end workflow:
- Image upload and validation
- Processing pipeline with Bull queues
- Real-time WebSocket updates
- Database persistence
- Error handling and recovery
- Performance under load

## Test Structure

```
tests/
├── e2e/                    # End-to-end integration tests
│   └── end-to-end-flow.test.ts
├── performance/            # Load and performance tests
│   └── load-test.ts
└── integration/            # Component integration tests

scripts/
├── run-e2e-tests.ts       # Automated test runner
└── validate-test-environment.ts

docs/testing/
├── README.md               # This file
└── end-to-end-manual-test-guide.md
```

## Quick Start

### 1. Environment Setup

Ensure all services are running:

```bash
# Terminal 1: Start backend server
npm run dev:server

# Terminal 2: Start worker process
npm run dev:worker

# Terminal 3: Start frontend (optional for API tests)
npm run dev:client

# Terminal 4: Validate environment
npm run validate-env
```

### 2. Run Tests

```bash
# Run all tests
npm run test:all

# Run only end-to-end tests
npm run test:e2e

# Run performance tests
npm run test:performance

# Run full automated test suite
npm run test:e2e-full
```

## Test Categories

### 1. End-to-End Tests (`tests/e2e/`)

Tests the complete workflow from image upload to processing completion:

- **Authentication Flow**: User login/registration
- **WebSocket Connection**: Real-time communication setup
- **Image Upload**: File validation and upload process
- **Processing Pipeline**: Job creation and execution
- **Real-time Updates**: WebSocket progress broadcasting
- **Error Handling**: Various failure scenarios

**Run with:**
```bash
npm run test:e2e
```

### 2. Performance Tests (`tests/performance/`)

Tests system performance under load:

- **Concurrent Uploads**: Multiple simultaneous file uploads
- **WebSocket Load**: Multiple concurrent connections
- **Memory Usage**: Memory leak detection
- **Response Times**: Performance benchmarking

**Run with:**
```bash
npm run test:performance
```

### 3. Manual Testing

Follow the comprehensive manual testing guide:

```bash
# Open the manual testing guide
open docs/testing/end-to-end-manual-test-guide.md
```

## Test Configuration

### Environment Variables

Set these for testing:

```bash
NODE_ENV=test
REDIS_URL=redis://localhost:6379
DATABASE_URL=file:./test.db
```

### Test Data

Tests automatically create and clean up:
- Test user accounts
- Test images (PNG/JPEG)
- Processing jobs
- WebSocket connections

## Automated Test Runner

The automated test runner (`scripts/run-e2e-tests.ts`) provides:

- **Service Management**: Starts/stops required services
- **Environment Validation**: Checks all dependencies
- **Test Execution**: Runs all test suites
- **Report Generation**: Creates HTML and JSON reports
- **Cleanup**: Proper resource cleanup

**Usage:**
```bash
npm run test:e2e-full
```

**Features:**
- Automatic service startup
- Health checks before testing
- Detailed progress logging
- Performance metrics collection
- HTML test reports

## Environment Validation

Before running tests, validate your environment:

```bash
# Quick validation
tsx scripts/validate-test-environment.ts

# Or via npm script
npm run validate-env
```

**Checks:**
- Backend API availability
- WebSocket server connectivity
- Database operations
- Authentication endpoints
- Static file serving

## Test Reports

Test results are saved to `test-results/`:

```
test-results/
├── e2e-report.json         # Machine-readable results
├── e2e-report.html         # Human-readable report
└── performance-metrics.json
```

## Performance Benchmarks

### Expected Performance
- **Upload Time**: < 5 seconds for 5MB image
- **Processing Time**: < 30 seconds for typical image
- **WebSocket Latency**: < 100ms for progress updates
- **Memory Usage**: < 100MB for typical session

### Load Test Targets
- **Concurrent Uploads**: 5 simultaneous uploads
- **WebSocket Connections**: 10 concurrent connections
- **Success Rate**: > 90% under load
- **Error Rate**: < 10% under normal conditions

## Troubleshooting

### Common Issues

#### Tests Failing to Connect
```bash
# Check if services are running
curl http://localhost:3001/health
curl http://localhost:3001/ws/socket.io/?EIO=4&transport=polling

# Validate environment
npm run validate-env
```

#### WebSocket Connection Issues
```bash
# Check WebSocket server
npm run dev:server

# Check for port conflicts
netstat -an | grep 3001
```

#### Database Issues
```bash
# Reset test database
npx prisma db push --force-reset

# Check database connection
npx prisma studio
```

#### Redis Issues
```bash
# Start Redis server
redis-server

# Check Redis connection
redis-cli ping
```

### Debug Mode

Run tests with debug output:

```bash
# Enable debug logging
DEBUG=* npm run test:e2e

# Vitest debug mode
npm run test:e2e -- --reporter=verbose
```

## Contributing

### Adding New Tests

1. **End-to-End Tests**: Add to `tests/e2e/`
2. **Performance Tests**: Add to `tests/performance/`
3. **Manual Tests**: Update the manual testing guide

### Test Conventions

- Use descriptive test names
- Include setup and cleanup
- Add performance assertions
- Document expected behavior
- Handle async operations properly

### Example Test Structure

```typescript
describe('Feature Name', () => {
  beforeAll(async () => {
    // Global setup
  });

  afterAll(async () => {
    // Global cleanup
  });

  beforeEach(() => {
    // Test setup
  });

  it('should perform expected behavior', async () => {
    // Arrange
    const testData = setupTestData();

    // Act
    const result = await performAction(testData);

    // Assert
    expect(result).toMatchExpectedBehavior();
  });
});
```

## CI/CD Integration

### GitHub Actions

```yaml
name: E2E Tests
on: [push, pull_request]
jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Start services
        run: |
          npm run dev:server &
          npm run dev:worker &
      - name: Run E2E tests
        run: npm run test:e2e-full
```

### Local Development

```bash
# Pre-commit hook
npm run test:e2e

# Pre-push hook
npm run test:all
```

## Support

For testing issues:

1. Check the troubleshooting section
2. Validate your environment
3. Review test logs and reports
4. Check service health endpoints
5. Consult the manual testing guide
