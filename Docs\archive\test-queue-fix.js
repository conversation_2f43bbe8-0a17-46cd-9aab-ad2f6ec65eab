// Test script to verify Bull queue job processor fix
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Use dynamic import for node-fetch (ES module)
let fetch;
(async () => {
  const { default: nodeFetch } = await import('node-fetch');
  fetch = nodeFetch;
  testQueueProcessing();
})();

async function testQueueProcessing() {
  console.log('🧪 Testing Bull Queue Job Processor Fix...\n');

  try {
    // Step 1: Check if server is running
    console.log('1. Checking server health...');
    const healthResponse = await fetch('http://localhost:3001/api/health');
    if (!healthResponse.ok) {
      throw new Error('Server not running');
    }
    console.log('✓ Server is running\n');

    // Step 2: Create a test user and get auth token
    console.log('2. Creating test user...');
    const registerResponse = await fetch('http://localhost:3001/api/v1/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })
    });

    let authToken;
    if (registerResponse.status === 409) {
      // User already exists, try to login
      console.log('User exists, logging in...');
      const loginResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });
      const loginData = await loginResponse.json();
      authToken = loginData.data.token;
    } else {
      const registerData = await registerResponse.json();
      authToken = registerData.data.token;
    }
    console.log('✓ Authentication successful\n');

    // Step 3: Create a simple test image (1x1 pixel PNG)
    console.log('3. Creating test image...');
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    console.log('✓ Test image created\n');

    // Step 4: Upload the image
    console.log('4. Uploading image to test job processing...');
    const formData = new FormData();
    formData.append('file', testImageBuffer, {
      filename: 'test-image.png',
      contentType: 'image/png'
    });
    formData.append('mode', 'auto');

    const uploadResponse = await fetch('http://localhost:3001/api/v1/images/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadData = await uploadResponse.json();
    console.log('✓ Image uploaded successfully');
    console.log(`   Image ID: ${uploadData.data.imageId}`);
    console.log(`   Processing Status: ${uploadData.data.processingStatus}\n`);

    // Step 5: Wait and monitor for job processing
    console.log('5. Monitoring job processing...');
    console.log('   (Check the worker terminal for job processor messages)');
    console.log('   Looking for messages like: "🔄 Job processor started for job..."');
    
    // Wait a few seconds for processing
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('\n✅ Test completed!');
    console.log('\n📋 What to check:');
    console.log('   1. Worker terminal should show job processing messages');
    console.log('   2. Look for "🔄 Job processor started for job [ID]"');
    console.log('   3. Look for "🎉 Image processing completed for [imageId]"');
    console.log('   4. No errors should appear in either terminal');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Test function will be called from the dynamic import above
