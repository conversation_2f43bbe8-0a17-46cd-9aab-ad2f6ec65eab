{"timestamp": "2025-08-01T14:28:49.606Z", "totalDuration": 4318, "summary": {"totalTests": 9, "totalPassed": 3, "totalFailed": 6}, "suites": [{"name": "Environment Validation", "results": [{"name": "Node.js Version Check", "status": "PASS", "duration": 43, "details": "Node.js v22.14.0 ✅"}, {"name": "Docker Availability", "status": "PASS", "duration": 70, "details": "Docker available ✅"}, {"name": "Environment Variables", "status": "FAIL", "duration": 0, "details": "Missing: <PERSON><PERSON><PERSON><PERSON><PERSON>_URL, REDIS_URL, JWT_SECRET ❌"}], "totalDuration": 113, "passCount": 2, "failCount": 1, "skipCount": 0}, {"name": "Service Health Checks", "results": [{"name": "Service Startup", "status": "FAIL", "duration": 307, "details": "Service startup failed: Error: Command failed: npm run dev:services\nnpm error Missing script: \"dev:services\"\nnpm error\nnpm error Did you mean this?\nnpm error   npm run dev:server # run the \"dev:server\" package script\nnpm error\nnpm error To see a list of scripts, run:\nnpm error   npm run\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-08-01T14_28_45_628Z-debug-0.log\n ❌"}, {"name": "Environment Validation", "status": "FAIL", "duration": 722, "details": "Validation error: Error: Command failed: npm run validate-env\n ❌"}], "totalDuration": 1030, "passCount": 0, "failCount": 2, "skipCount": 0}, {"name": "Smoke Tests", "results": [{"name": "Smoke Tests", "status": "FAIL", "duration": 1154, "details": "Test execution failed: Error: Command failed: npm run test:smoke\n\u001b[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.\u001b[39m\n\u001b[31mNo test files found, exiting with code 1\n\u001b[39m\n\u001b[2mfilter: \u001b[22m\u001b[33mtests/smoke/\u001b[39m\n\u001b[2minclude: \u001b[22m\u001b[33m**/*.{test,spec}.?(c|m)[jt]s?(x)\u001b[39m\n\u001b[2mexclude:  \u001b[22m\u001b[33m**/node_modules/**\u001b[2m, \u001b[22m**/dist/**\u001b[2m, \u001b[22m**/cypress/**\u001b[2m, \u001b[22m**/.{idea,git,cache,output,temp}/**\u001b[2m, \u001b[22m**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build,eslint,prettier}.config.*\u001b[39m\n\n(node:62476) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///C:/Users/<USER>/projects/I2D-Convert/postcss.config.js?t=1754058527189 is not specified and it doesn't parse as CommonJS.\nReparsing as ES module because module syntax was detected. This incurs a performance overhead.\nTo eliminate this warning, add \"type\": \"module\" to C:\\Users\\<USER>\\projects\\I2D-Convert\\package.json.\n(Use `node --trace-warnings ...` to show where the warning was created)\n ❌"}], "totalDuration": 1154, "passCount": 0, "failCount": 1, "skipCount": 0}, {"name": "End-to-End Tests", "results": [{"name": "End-to-End Tests", "status": "PASS", "duration": 1025, "details": "Test failed as expected (rate limiting) ✅"}], "totalDuration": 1025, "passCount": 1, "failCount": 0, "skipCount": 0}, {"name": "Performance Tests", "results": [{"name": "WebSocket Performance", "status": "FAIL", "duration": 952, "details": "Performance test failed: Error: Command failed: npm run test:performance\n\u001b[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.\u001b[39m\n\u001b[31mNo test files found, exiting with code 1\n\u001b[39m\n\u001b[2mfilter: \u001b[22m\u001b[33mtests/performance/\u001b[39m\n\u001b[2minclude: \u001b[22m\u001b[33m**/*.{test,spec}.?(c|m)[jt]s?(x)\u001b[39m\n\u001b[2mexclude:  \u001b[22m\u001b[33m**/node_modules/**\u001b[2m, \u001b[22m**/dist/**\u001b[2m, \u001b[22m**/cypress/**\u001b[2m, \u001b[22m**/.{idea,git,cache,output,temp}/**\u001b[2m, \u001b[22m**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build,eslint,prettier}.config.*\u001b[39m\n\n(node:64700) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///C:/Users/<USER>/projects/I2D-Convert/postcss.config.js?t=1754058529277 is not specified and it doesn't parse as CommonJS.\nReparsing as ES module because module syntax was detected. This incurs a performance overhead.\nTo eliminate this warning, add \"type\": \"module\" to C:\\Users\\<USER>\\projects\\I2D-Convert\\package.json.\n(Use `node --trace-warnings ...` to show where the warning was created)\n ❌"}], "totalDuration": 952, "passCount": 0, "failCount": 1, "skipCount": 0}, {"name": "Security Tests", "results": [{"name": "Rate Limiting", "status": "FAIL", "duration": 41, "details": "Rate limiting test failed: TypeError: fetch failed ❌"}], "totalDuration": 41, "passCount": 0, "failCount": 1, "skipCount": 0}]}