const { Client } = require('pg');

async function testPgConnection() {
  const client = new Client({
    host: '127.0.0.1',
    port: 5432,
    database: 'i2d_convert',
    user: 'i2d_user',
    password: 'password123',
  });

  try {
    console.log('Testing PostgreSQL connection with pg client...');
    await client.connect();
    console.log('✅ PostgreSQL connection successful!');
    
    const result = await client.query('SELECT current_user, current_database()');
    console.log('Query result:', result.rows);
    
  } catch (error) {
    console.error('❌ PostgreSQL connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Full error:', error);
  } finally {
    await client.end();
  }
}

testPgConnection();
