#!/usr/bin/env tsx

/**
 * Basic Upload Test Script
 * 
 * Tests the core image upload and processing functionality without WebSocket
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';

// Configuration
const API_BASE_URL = 'http://localhost:3001/api/v1';
const TEST_IMAGE_PATH = path.join(__dirname, 'test-image.png');

class BasicUploadTester {
  private authToken: string = '';

  async run(): Promise<void> {
    console.log('🚀 Starting Basic Upload Test...\n');

    try {
      // Step 1: Setup
      await this.setup();
      
      // Step 2: Test image upload
      const uploadResult = await this.testImageUpload();
      
      // Step 3: Test job status tracking
      if (uploadResult) {
        await this.testJobStatusTracking(uploadResult.imageId, uploadResult.jobId);
      }
      
      // Step 4: Test status badge scenarios
      await this.testStatusBadgeScenarios();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  private async setup(): Promise<void> {
    console.log('📋 Setting up test environment...');
    
    // Create test image
    await this.createTestImage();
    
    // Register/login test user
    await this.authenticateTestUser();
    
    console.log('✅ Setup completed\n');
  }

  private async createTestImage(): Promise<void> {
    // Create a simple test PNG image (100x100 pixels)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
      0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x64, // 100x100 dimensions
      0x08, 0x02, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x02, // bit depth, color type, etc.
      0x03, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
      0x54, 0x08, 0x99, 0x01, 0x01, 0x01, 0x00, 0x00, // image data
      0xFE, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // more data
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
      0xAE, 0x42, 0x60, 0x82
    ]);

    fs.writeFileSync(TEST_IMAGE_PATH, testImageBuffer);
    console.log(`📸 Test image created: ${TEST_IMAGE_PATH}`);
  }

  private async authenticateTestUser(): Promise<void> {
    const timestamp = Date.now();
    const testUser = {
      email: `basic-test-${timestamp}@example.com`,
      password: 'TestPassword123!',
      name: 'Basic Test User'
    };

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, testUser);
      console.log('📊 Register Response:', JSON.stringify(response.data, null, 2));

      if (response.data.success && response.data.data.token) {
        this.authToken = response.data.data.token;
        console.log('🔐 Test user authenticated');
      } else {
        throw new Error('Registration response missing token');
      }
    } catch (error: any) {
      if (error.response?.status === 429) {
        // Rate limited, try to login instead
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });
        console.log('📊 Login Response:', JSON.stringify(loginResponse.data, null, 2));

        if (loginResponse.data.success && loginResponse.data.data.token) {
          this.authToken = loginResponse.data.data.token;
          console.log('🔐 Test user logged in (rate limited)');
        } else {
          throw new Error('Login response missing token');
        }
      } else {
        console.error('Authentication error details:', error.response?.data);
        throw new Error(`Authentication failed: ${error.message}`);
      }
    }
  }

  private async testImageUpload(): Promise<{imageId: string, jobId: string} | null> {
    console.log('📤 Testing fresh image upload...');
    
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(TEST_IMAGE_PATH));
      formData.append('mode', 'ui');
      formData.append('processingType', 'standard');

      const response = await axios.post(`${API_BASE_URL}/images/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          ...formData.getHeaders()
        }
      });

      console.log('📊 Upload Response Status:', response.status);
      console.log('📊 Upload Response Data:', JSON.stringify(response.data, null, 2));

      if ((response.status === 200 || response.status === 201) && response.data.success) {
        console.log('✅ Image upload successful');
        console.log(`   Image ID: ${response.data.data.imageId}`);
        console.log(`   Processing Status: ${response.data.data.processingStatus}`);

        // Get the job ID by querying the processing jobs for this image
        const jobId = await this.getJobIdForImage(response.data.data.imageId);

        return {
          imageId: response.data.data.imageId,
          jobId: jobId
        };
      } else {
        throw new Error(`Upload failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      console.error('❌ Image upload failed:', error.message);
      if (error.response) {
        console.error('   Response status:', error.response.status);
        console.error('   Response data:', error.response.data);
      }
      return null;
    }
  }

  private async getJobIdForImage(imageId: string): Promise<string> {
    try {
      // Get all processing jobs and find the one for this image
      const response = await axios.get(`${API_BASE_URL}/processing/jobs`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` },
        params: { limit: 50 }
      });

      if (response.data.success) {
        const jobs = response.data.data;
        const job = jobs.find((j: any) => j.imageId === imageId);
        if (job) {
          console.log(`   Job ID: ${job.id}`);
          return job.id;
        }
      }
      throw new Error('No job found for image');
    } catch (error: any) {
      console.error('Failed to get job ID:', error.message);
      return 'unknown';
    }
  }

  private async testJobStatusTracking(imageId: string, jobId: string): Promise<void> {
    console.log('\n📋 Testing job status tracking...');
    
    try {
      // Test job status endpoint
      const response = await axios.get(`${API_BASE_URL}/processing/jobs/${jobId}`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` }
      });

      console.log('📊 Job Status Response:', JSON.stringify(response.data, null, 2));

      if (response.status === 200 && response.data.success) {
        const job = response.data.data;
        console.log('✅ Job tracking successful');
        console.log(`   Status: ${job.status}`);
        console.log(`   Progress: ${Math.round((job.progress || 0) * 100)}%`);
        console.log(`   Created: ${job.createdAt}`);
        console.log(`   Started: ${job.startedAt || 'Not started'}`);
        
        // Monitor job progress for a few iterations
        await this.monitorJobProgress(jobId, 60); // Monitor for 60 seconds max
      } else {
        throw new Error('Job status check failed');
      }
    } catch (error: any) {
      console.error('❌ Job tracking failed:', error.message);
      if (error.response) {
        console.error('   Response status:', error.response.status);
        console.error('   Response data:', error.response.data);
      }
    }
  }

  private async monitorJobProgress(jobId: string, maxSeconds: number): Promise<void> {
    console.log(`\n⏳ Monitoring job progress for up to ${maxSeconds} seconds...`);
    
    let attempts = 0;
    const maxAttempts = maxSeconds;
    
    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(`${API_BASE_URL}/processing/jobs/${jobId}`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });

        if (response.data.success) {
          const job = response.data.data;
          const progress = Math.round((job.progress || 0) * 100);
          console.log(`📊 [${attempts + 1}s] Status: ${job.status} | Progress: ${progress}%`);
          
          if (job.status === 'COMPLETED') {
            console.log('✅ Job completed successfully!');
            console.log(`   Final status: ${job.status}`);
            console.log(`   Processing time: ${job.processingTime || 'N/A'}ms`);
            if (job.output) {
              console.log(`   Output keys: ${Object.keys(job.output).join(', ')}`);
            }
            break;
          } else if (job.status === 'FAILED') {
            console.error('❌ Job failed:', job.error);
            break;
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      } catch (error: any) {
        console.error(`❌ Progress check failed: ${error.message}`);
        break;
      }
    }

    if (attempts >= maxAttempts) {
      console.log('⚠️  Monitoring timeout reached');
    }
  }

  private async testStatusBadgeScenarios(): Promise<void> {
    console.log('\n🏷️  Testing status badge scenarios...');
    
    try {
      // Get all processing jobs to see different statuses
      const response = await axios.get(`${API_BASE_URL}/processing/jobs`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` },
        params: { limit: 10 }
      });

      if (response.data.success) {
        const jobs = response.data.data;
        console.log(`✅ Found ${jobs.length} jobs for status badge testing:`);
        
        jobs.forEach((job: any, index: number) => {
          console.log(`   ${index + 1}. Job ${job.id.slice(0, 8)}... - Status: ${job.status} (${Math.round((job.progress || 0) * 100)}%)`);
        });

        // Test different status badge colors/styles
        const statusCounts = jobs.reduce((acc: any, job: any) => {
          acc[job.status] = (acc[job.status] || 0) + 1;
          return acc;
        }, {});

        console.log('\n📊 Status distribution for badge testing:');
        Object.entries(statusCounts).forEach(([status, count]) => {
          const badgeColor = this.getStatusBadgeColor(status);
          console.log(`   ${status}: ${count} jobs (Badge: ${badgeColor})`);
        });
      }
    } catch (error: any) {
      console.error('❌ Status badge testing failed:', error.message);
    }
  }

  private getStatusBadgeColor(status: string): string {
    switch (status) {
      case 'COMPLETED': return 'green';
      case 'PROCESSING': 
      case 'RUNNING': return 'blue';
      case 'QUEUED': return 'yellow';
      case 'FAILED': return 'red';
      default: return 'gray';
    }
  }

  private async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up...');
    
    if (fs.existsSync(TEST_IMAGE_PATH)) {
      fs.unlinkSync(TEST_IMAGE_PATH);
      console.log('🗑️  Test image deleted');
    }
    
    console.log('✅ Cleanup completed');
  }
}

// Run the test
if (require.main === module) {
  const tester = new BasicUploadTester();
  tester.run().catch(console.error);
}

export default BasicUploadTester;
