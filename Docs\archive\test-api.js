const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001/api/v1';

async function testAPI() {
  console.log('🧪 Testing I2D Convert API...\n');

  const testEmail = `test${Date.now()}@example.com`;

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await fetch('http://localhost:3001/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);

    // Test 2: API info
    console.log('\n2. Testing API info...');
    const infoResponse = await fetch(`${BASE_URL}`);
    const infoData = await infoResponse.json();
    console.log('✅ API info:', infoData.name);

    // Test 3: Register user
    console.log('\n3. Testing user registration...');
    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testEmail,
        password: 'testpassword123',
        name: 'Test User'
      })
    });
    
    const registerData = await registerResponse.json();
    if (registerData.success) {
      console.log('✅ User registration successful');
      console.log('   User ID:', registerData.data.user.id);
      console.log('   Token received:', registerData.data.token ? 'Yes' : 'No');
      
      // Test 4: Login with the same user
      console.log('\n4. Testing user login...');
      const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail,
          password: 'testpassword123'
        })
      });
      
      const loginData = await loginResponse.json();
      if (loginData.success) {
        console.log('✅ User login successful');
        console.log('   User ID:', loginData.data.user.id);
        
        // Test 5: Verify token
        console.log('\n5. Testing token verification...');
        const verifyResponse = await fetch(`${BASE_URL}/auth/verify`, {
          headers: {
            'Authorization': `Bearer ${loginData.data.token}`
          }
        });
        
        const verifyData = await verifyResponse.json();
        if (verifyData.success) {
          console.log('✅ Token verification successful');
          console.log('   Verified user:', verifyData.data.user.email);

          // Test 6: Get user profile
          console.log('\n6. Testing user profile...');
          const profileResponse = await fetch(`${BASE_URL}/users/me`, {
            headers: {
              'Authorization': `Bearer ${loginData.data.token}`
            }
          });

          const profileData = await profileResponse.json();
          if (profileData.success) {
            console.log('✅ User profile retrieved successfully');
            console.log('   User name:', profileData.data.name);
            console.log('   Token balance:', profileData.data.tokenBalance);
          } else {
            console.log('❌ User profile failed:', profileData.error);
          }

          // Test 7: Get user stats
          console.log('\n7. Testing user stats...');
          const statsResponse = await fetch(`${BASE_URL}/users/me/stats`, {
            headers: {
              'Authorization': `Bearer ${loginData.data.token}`
            }
          });

          const statsData = await statsResponse.json();
          if (statsData.success) {
            console.log('✅ User stats retrieved successfully');
            console.log('   Total images:', statsData.data.totalImages);
            console.log('   Total projects:', statsData.data.totalProjects);
          } else {
            console.log('❌ User stats failed:', statsData.error);
          }
        } else {
          console.log('❌ Token verification failed:', verifyData.error);
        }
      } else {
        console.log('❌ User login failed:', loginData.error);
      }
    } else {
      console.log('❌ User registration failed:', registerData.error);
    }

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

testAPI();
