// Load environment variables FIRST before any other imports
import dotenv from 'dotenv';
dotenv.config();

class WorkerProcess {
  private isShuttingDown = false;
  private QueueService: any;
  private RedisService: any;
  private DatabaseService: any;

  async start(): Promise<void> {
    console.log('🔧 Starting I2D Convert Worker Process...\n');

    try {
      // Import services after dotenv is loaded
      const { QueueService } = await import('./services/queue');
      const { RedisService } = await import('./services/redis');
      const { DatabaseService } = await import('./services/database');

      // Store services as class properties
      this.QueueService = QueueService;
      this.RedisService = RedisService;
      this.DatabaseService = DatabaseService;

      // Initialize services
      await this.initializeServices();

      // Set up graceful shutdown
      this.setupGracefulShutdown();

      console.log('✅ Worker process started successfully');
      console.log('📋 Listening for jobs...\n');

      // Keep the process alive
      this.keepAlive();

    } catch (error) {
      console.error('❌ Failed to start worker process:', error);
      process.exit(1);
    }
  }

  private async initializeServices(): Promise<void> {
    // Initialize database
    await this.DatabaseService.initialize();
    console.log('✓ Database initialized');

    // Initialize Redis
    await this.RedisService.initialize();
    console.log('✓ Redis initialized');

    // Initialize Queue service in consumer mode (for job processing)
    await this.QueueService.initialize('consumer');
    console.log('✓ Queue service initialized in consumer mode');
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        console.log('Force shutdown...');
        process.exit(1);
      }

      this.isShuttingDown = true;
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);

      try {
        // Stop accepting new jobs and wait for current jobs to complete
        console.log('⏳ Waiting for current jobs to complete...');
        await this.QueueService.disconnect();
        console.log('✓ Queue service disconnected');

        await this.RedisService.disconnect();
        console.log('✓ Redis disconnected');

        console.log('✅ Worker process shut down gracefully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // For nodemon
  }

  private keepAlive(): void {
    // Log periodic status updates
    setInterval(async () => {
      if (!this.isShuttingDown) {
        try {
          const stats = await this.QueueService.getQueueStats();
          const redisHealth = await this.RedisService.healthCheck();
          
          console.log(`📊 Worker Status - Redis: ${redisHealth ? 'OK' : 'ERROR'}`);
          console.log(`   Queue Stats:`, JSON.stringify(stats, null, 2));
        } catch (error) {
          console.error('Error getting worker status:', error);
        }
      }
    }, 30000); // Every 30 seconds

    // Keep process alive
    setInterval(() => {
      if (this.isShuttingDown) {
        return;
      }
      // Just keep the event loop alive
    }, 1000);
  }
}

// Start the worker if this file is run directly
if (require.main === module) {
  const worker = new WorkerProcess();
  worker.start().catch((error) => {
    console.error('Failed to start worker:', error);
    process.exit(1);
  });
}

export default WorkerProcess;
