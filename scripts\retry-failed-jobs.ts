import dotenv from 'dotenv';
dotenv.config();

async function retryFailedJobs() {
  try {
    console.log('🔍 Checking for failed jobs...\n');

    // Import services after dotenv is loaded
    const { QueueService } = await import('../src/server/services/queue');
    const { RedisService } = await import('../src/server/services/redis');

    // Initialize services
    await RedisService.initialize();
    await QueueService.initialize('producer');

    // Get failed jobs
    const failedJobs = await QueueService.getFailedJobs();
    console.log(`❌ Found ${failedJobs.length} failed jobs:`);
    
    if (failedJobs.length === 0) {
      console.log('✅ No failed jobs found.');
      return;
    }

    failedJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id}, Data:`, job.data);
      console.log(`     Error: ${job.failedReason}`);
    });

    console.log('\n🔄 Retrying failed jobs...\n');

    for (const job of failedJobs) {
      try {
        console.log(`🔄 Retrying job ${job.id}...`);
        await job.retry();
        console.log(`  ✓ Job ${job.id} retried successfully`);
      } catch (error) {
        console.error(`  ❌ Failed to retry job ${job.id}:`, error);
      }
    }

    console.log('\n✅ Finished retrying failed jobs');

    // Wait a moment and check queue stats
    console.log('\n📊 Waiting 3 seconds and checking queue stats...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    const stats = await QueueService.getQueueStats();
    console.log('📊 Updated Queue Statistics:');
    console.log(`  - Waiting: ${stats.waiting}`);
    console.log(`  - Active: ${stats.active}`);
    console.log(`  - Completed: ${stats.completed}`);
    console.log(`  - Failed: ${stats.failed}`);

  } catch (error) {
    console.error('❌ Error retrying failed jobs:', error);
  } finally {
    process.exit(0);
  }
}

retryFailedJobs();
