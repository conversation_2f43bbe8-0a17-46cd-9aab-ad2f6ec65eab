import dotenv from 'dotenv';
dotenv.config();

import { QueueService } from '../src/server/services/queue';
import { RedisService } from '../src/server/services/redis';

async function checkQueueStatus() {
  try {
    console.log('🔍 Checking queue status...\n');

    // Initialize services
    await RedisService.initialize();
    await QueueService.initialize('producer');

    // Get queue stats
    const stats = await QueueService.getQueueStats();
    console.log('📊 Queue Statistics:');
    console.log(`  - Waiting: ${stats.waiting}`);
    console.log(`  - Active: ${stats.active}`);
    console.log(`  - Completed: ${stats.completed}`);
    console.log(`  - Failed: ${stats.failed}`);
    console.log(`  - Delayed: ${stats.delayed}`);
    console.log(`  - Paused: ${stats.paused}\n`);

    // Get waiting jobs
    const waitingJobs = await QueueService.getWaitingJobs();
    console.log(`📋 Waiting Jobs (${waitingJobs.length}):`);
    waitingJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id}, Data:`, job.data);
    });

    // Get active jobs
    const activeJobs = await QueueService.getActiveJobs();
    console.log(`\n⚡ Active Jobs (${activeJobs.length}):`);
    activeJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id}, Data:`, job.data);
    });

    // Get completed jobs
    const completedJobs = await QueueService.getCompletedJobs();
    console.log(`\n✅ Completed Jobs (${completedJobs.length}):`);
    completedJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id}, Data:`, job.data);
    });

    // Get failed jobs
    const failedJobs = await QueueService.getFailedJobs();
    console.log(`\n❌ Failed Jobs (${failedJobs.length}):`);
    failedJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id}, Error:`, job.failedReason);
    });

  } catch (error) {
    console.error('❌ Error checking queue status:', error);
  } finally {
    await QueueService.disconnect();
    await RedisService.disconnect();
    process.exit(0);
  }
}

checkQueueStatus();
