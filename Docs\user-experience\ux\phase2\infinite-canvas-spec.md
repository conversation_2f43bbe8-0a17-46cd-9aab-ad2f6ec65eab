# Phase 2: Infinite Canvas Specification

**Document Version:** 1.0  
**Date:** 31 July 2025  
**Project:** I2D-Convert Infinite Canvas Implementation  
**Maintainer:** Kanousei Technology LLC

---

## 1. Canvas Architecture Overview

### 1.1 Technology Stack
**Primary Framework**: React Konva (based on analysis of fal-ai infinite-kanvas)
- **Rendering Engine**: Konva.js for 2D canvas manipulation
- **React Integration**: react-konva for component-based canvas development
- **Performance**: Hardware-accelerated rendering with viewport culling
- **Event System**: Built-in mouse, touch, and keyboard event handling

### 1.2 Layer Architecture
```
Canvas Container
├── Background Layer (Static)
│   └── Original uploaded image
├── Wireframe Layer (Interactive)
│   ├── Detected component boundaries
│   ├── Bounding boxes with labels
│   └── Confidence score indicators
├── Component Layer (Interactive)
│   ├── Mapped design system components
│   ├── Component previews and variants
│   └── Drag-and-drop targets
├── Annotation Layer (Interactive)
│   ├── User comments and feedback
│   ├── Issue markers and highlights
│   └── Measurement tools and guides
└── UI Layer (Overlay)
    ├── Canvas controls and toolbar
    ├── Context menus and tooltips
    └── Selection handles and manipulators
```

### 1.3 Coordinate System
- **World Coordinates**: Absolute positioning relative to original image
- **Screen Coordinates**: Viewport-relative positioning for UI elements
- **Transform Matrix**: Handles zoom, pan, and rotation transformations
- **Responsive Scaling**: Maintains aspect ratios across device sizes

---

## 2. Core Canvas Features

### 2.1 Viewport Management
```typescript
interface ViewportState {
  x: number;           // Pan X offset
  y: number;           // Pan Y offset
  scaleX: number;      // Zoom level X
  scaleY: number;      // Zoom level Y (usually same as scaleX)
  rotation: number;    // Canvas rotation (future feature)
}

interface ViewportControls {
  pan(deltaX: number, deltaY: number): void;
  zoom(factor: number, centerX?: number, centerY?: number): void;
  fitToScreen(): void;
  resetView(): void;
  zoomToSelection(): void;
}
```

### 2.2 Navigation Controls
**Mouse/Trackpad**:
- Pan: Click and drag (or middle mouse button)
- Zoom: Mouse wheel or pinch gesture
- Select: Left click on elements

**Keyboard Shortcuts**:
- `Space + Drag`: Pan canvas
- `Ctrl/Cmd + Scroll`: Zoom in/out
- `Ctrl/Cmd + 0`: Fit to screen
- `Ctrl/Cmd + 1`: Actual size (100%)
- `Arrow Keys`: Nudge selected elements

**Touch Gestures**:
- Single finger drag: Pan canvas
- Pinch: Zoom in/out
- Tap: Select elements
- Long press: Context menu

### 2.3 Performance Optimizations
```typescript
interface PerformanceConfig {
  viewportCulling: boolean;     // Only render visible elements
  lazyLoading: boolean;         // Load images progressively
  batchUpdates: boolean;        // Group canvas updates
  maxZoomLevel: number;         // Prevent excessive zoom
  minZoomLevel: number;         // Maintain readability
  renderThrottle: number;       // Limit render frequency (ms)
}
```

---

## 3. Tab-Specific Canvas Behavior

### 3.1 Wireframe Tab
**Purpose**: Review and edit AI-detected component boundaries

**Canvas Elements**:
- Original image as background (slightly dimmed)
- Grayscale overlay showing detected structure
- Bounding boxes with component labels
- Confidence scores as color-coded indicators
- Editable handles for boundary adjustment

**Interactions**:
- Click to select detected components
- Drag handles to resize boundaries
- Double-click to edit component labels
- Right-click for context menu (delete, split, merge)

**Visual Design**:
```
┌─────────────────────────────────────────────────────────────┐
│ [Fit] [100%] [Zoom In] [Zoom Out]    Wireframe    [Grid]   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│    ┌─────────────────────────────────────────────────┐     │
│    │                                                 │     │
│    │  ┌─────────────────┐  ┌─────────────────┐      │     │
│    │  │ Button (95%)    │  │ Input (87%)     │      │     │
│    │  │ [Resize Handle] │  │ [Resize Handle] │      │     │
│    │  └─────────────────┘  └─────────────────┘      │     │
│    │                                                 │     │
│    │  ┌─────────────────────────────────────────┐   │     │
│    │  │ Text Block (92%)                        │   │     │
│    │  │ [Resize Handle]                         │   │     │
│    │  └─────────────────────────────────────────┘   │     │
│    │                                                 │     │
│    └─────────────────────────────────────────────────┘     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Components Tab
**Purpose**: Map detected elements to design system components

**Canvas Elements**:
- Original image as reference
- Component library sidebar
- Drag-and-drop targets for component mapping
- Component previews overlaid on original elements
- Hierarchy tree showing component relationships

**Interactions**:
- Drag components from library to canvas
- Swap components by dropping replacements
- Adjust component properties in side panel
- Group/ungroup related components

**Visual Design**:
```
┌─────────────────────────────────────────────────────────────┐
│ Components                                    [Library ▼]   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ │                                           │
│ │ Component   │ │  ┌─────────────────────────────────────┐ │
│ │ Library     │ │  │                                     │ │
│ │             │ │  │  ┌─────────────┐  ┌─────────────┐  │ │
│ │ [Button]    │ │  │  │ Primary Btn │  │ Text Input  │  │ │
│ │ [Input]     │ │  │  │ ✓ Mapped    │  │ ✓ Mapped    │  │ │
│ │ [Card]      │ │  │  └─────────────┘  └─────────────┘  │ │
│ │ [Text]      │ │  │                                     │ │
│ │ [Icon]      │ │  │  ┌─────────────────────────────┐   │ │
│ │             │ │  │  │ Card Component              │   │ │
│ │ [Search...] │ │  │  │ ⚠ Needs mapping             │   │ │
│ │             │ │  │  └─────────────────────────────┘   │ │
│ └─────────────┘ │  │                                     │ │
│                 │  └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.3 Style Tab
**Purpose**: Apply design tokens and styling

**Canvas Elements**:
- Component-focused view with style previews
- Design token panel (colors, typography, spacing)
- Style application indicators
- Responsive preview modes

**Interactions**:
- Select components to view/edit styles
- Apply design tokens via drag-drop or click
- Preview responsive behavior
- Batch apply styles to similar components

**Visual Design**:
```
┌─────────────────────────────────────────────────────────────┐
│ Style                                      [Mobile] [Tablet]│
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ │                                           │
│ │ Design      │ │  ┌─────────────────────────────────────┐ │
│ │ Tokens      │ │  │                                     │ │
│ │             │ │  │  ┌─────────────┐  ┌─────────────┐  │ │
│ │ Colors:     │ │  │  │ Primary Btn │  │ Text Input  │  │ │
│ │ ● Primary   │ │  │  │ #007AFF     │  │ Font: 16px  │  │ │
│ │ ● Secondary │ │  │  └─────────────┘  └─────────────┘  │ │
│ │ ● Success   │ │  │                                     │ │
│ │             │ │  │  ┌─────────────────────────────┐   │ │
│ │ Typography: │ │  │  │ Card Component              │   │ │
│ │ H1: 32px    │ │  │  │ Padding: 16px               │   │ │
│ │ H2: 24px    │ │  │  │ Border: 1px solid #E5E5E5   │   │ │
│ │ Body: 16px  │ │  │  └─────────────────────────────┘   │ │
│ │             │ │  │                                     │ │
│ └─────────────┘ │  └─────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.4 Review Tab
**Purpose**: Quality assurance and export preparation

**Canvas Elements**:
- Final preview with all applied changes
- Issue markers and quality indicators
- Export preview in different formats
- Accessibility compliance indicators

**Interactions**:
- Review and resolve flagged issues
- Preview export in different formats
- Run quality checks and accessibility audits
- Export final code or assets

**Visual Design**:
```
┌─────────────────────────────────────────────────────────────┐
│ Review                                    [Export] [Share]  │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ │                                           │
│ │ Issues (3)  │ │  ┌─────────────────────────────────────┐ │
│ │             │ │  │                                     │ │
│ │ ⚠ Color     │ │  │  ┌─────────────┐  ┌─────────────┐  │ │
│ │   contrast  │ │  │  │ Primary Btn │  │ Text Input  │  │ │
│ │             │ │  │  │ ✓ Ready     │  │ ✓ Ready     │  │ │
│ │ ⚠ Missing   │ │  │  └─────────────┘  └─────────────┘  │ │
│ │   alt text  │ │  │                                     │ │
│ │             │ │  │  ┌─────────────────────────────┐   │ │
│ │ ℹ Spacing   │ │  │  │ Card Component              │   │ │
│ │   suggestion│ │  │  │ ✓ Ready                     │   │ │
│ │             │ │  │  └─────────────────────────────┘   │ │
│ │ Quality:    │ │  │                                     │ │
│ │ ████████ 85%│ │  └─────────────────────────────────────┘ │
│ └─────────────┘ │                                           │
└─────────────────────────────────────────────────────────────┘
```

---

## 4. Canvas Implementation Details

### 4.1 React Konva Setup
```typescript
import { Stage, Layer, Image, Rect, Text } from 'react-konva';

interface CanvasProps {
  width: number;
  height: number;
  imageUrl: string;
  components: DetectedComponent[];
  activeTab: 'wireframe' | 'components' | 'style' | 'review';
}

const InfiniteCanvas: React.FC<CanvasProps> = ({
  width,
  height,
  imageUrl,
  components,
  activeTab
}) => {
  const [stagePos, setStagePos] = useState({ x: 0, y: 0 });
  const [stageScale, setStageScale] = useState(1);
  
  return (
    <Stage
      width={width}
      height={height}
      scaleX={stageScale}
      scaleY={stageScale}
      x={stagePos.x}
      y={stagePos.y}
      draggable
      onWheel={handleWheel}
      onDragEnd={handleDragEnd}
    >
      <Layer>
        {/* Background image */}
        <BackgroundImage src={imageUrl} />
        
        {/* Tab-specific content */}
        {activeTab === 'wireframe' && (
          <WireframeLayer components={components} />
        )}
        {activeTab === 'components' && (
          <ComponentLayer components={components} />
        )}
        {activeTab === 'style' && (
          <StyleLayer components={components} />
        )}
        {activeTab === 'review' && (
          <ReviewLayer components={components} />
        )}
      </Layer>
    </Stage>
  );
};
```

### 4.2 Performance Optimizations
```typescript
// Viewport culling for large canvases
const useViewportCulling = (elements: CanvasElement[], viewport: Viewport) => {
  return useMemo(() => {
    return elements.filter(element => {
      return isElementInViewport(element, viewport);
    });
  }, [elements, viewport]);
};

// Lazy loading for images
const useLazyImage = (src: string) => {
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  
  useEffect(() => {
    const img = new window.Image();
    img.onload = () => setImage(img);
    img.src = src;
  }, [src]);
  
  return image;
};

// Throttled rendering
const useThrottledRender = (callback: () => void, delay: number) => {
  const throttledCallback = useCallback(
    throttle(callback, delay),
    [callback, delay]
  );
  
  return throttledCallback;
};
```

### 4.3 Event Handling
```typescript
interface CanvasEvents {
  onElementSelect: (element: CanvasElement) => void;
  onElementMove: (element: CanvasElement, newPosition: Position) => void;
  onElementResize: (element: CanvasElement, newSize: Size) => void;
  onCanvasPan: (delta: Position) => void;
  onCanvasZoom: (factor: number, center: Position) => void;
}

const handleElementClick = (e: KonvaEventObject<MouseEvent>) => {
  const element = e.target;
  onElementSelect(element.attrs as CanvasElement);
};

const handleElementDrag = (e: KonvaEventObject<DragEvent>) => {
  const element = e.target;
  const newPosition = { x: element.x(), y: element.y() };
  onElementMove(element.attrs as CanvasElement, newPosition);
};
```

---

## 5. Integration with 4-Tab Workflow

### 5.1 State Management
```typescript
interface CanvasState {
  originalImage: string;
  detectedComponents: DetectedComponent[];
  mappedComponents: MappedComponent[];
  appliedStyles: StyleApplication[];
  issues: QualityIssue[];
  viewport: ViewportState;
  selection: SelectedElement[];
}

const useCanvasState = () => {
  const [state, setState] = useState<CanvasState>(initialState);
  
  const updateComponents = (components: DetectedComponent[]) => {
    setState(prev => ({ ...prev, detectedComponents: components }));
  };
  
  const mapComponent = (detected: DetectedComponent, mapped: ComponentMapping) => {
    setState(prev => ({
      ...prev,
      mappedComponents: [...prev.mappedComponents, { detected, mapped }]
    }));
  };
  
  return { state, updateComponents, mapComponent };
};
```

### 5.2 Tab Synchronization
```typescript
const useTabSync = (canvasState: CanvasState) => {
  // Sync selection across tabs
  useEffect(() => {
    // When selection changes, update all tab views
    syncSelectionAcrossTabs(canvasState.selection);
  }, [canvasState.selection]);
  
  // Sync viewport across tabs
  useEffect(() => {
    // Maintain viewport position when switching tabs
    maintainViewportPosition(canvasState.viewport);
  }, [canvasState.viewport]);
};
```

---

*This specification provides the technical foundation for implementing the infinite canvas as the core interface for the I2D-Convert 4-tab workflow.*
