# I2D-Convert Layout Specification

> **📋 UX/UI Integration Update**: This technical layout specification is now complemented by comprehensive UX/UI documentation in `Docs/ux/` including user journeys, detailed wireframes, and infinite canvas specifications.

Below is a **pure-JSON wireframe specification**—no PNGs, just structured positioning data that your front-end team can ingest directly (or pump into Figma / Storybook importers). It describes the desktop layout (1440 × 900), plus override blocks for tablet (834 × 1112) and mobile (390 × 844). Adjust dimensions or add extra breakpoints as needed.

```jsonc
{
  "meta": {
    "created": "2025-07-31T09:00:00+04:00",
    "author": "Kanousei Technology LLC",
    "description": "Top-level wireframe for Image-to-Design-System Converter"
  },

  /* =====  Desktop: 1440 × 900 ===== */
  "desktop": {
    "screen": { "width": 1440, "height": 900 },

    "navBar": { "x": 0, "y": 0, "w": 1440, "h": 56, "role": "navigation" },

    "tabs": [
      { "id": "tab-wireframe",  "label": "Wireframe",  "x": 24,  "y": 0, "w": 150, "h": 56 },
      { "id": "tab-components", "label": "Components", "x": 190, "y": 0, "w": 160, "h": 56 },
      { "id": "tab-style",      "label": "Style",      "x": 366, "y": 0, "w": 110, "h": 56 },
      { "id": "tab-review",     "label": "Review",     "x": 494, "y": 0, "w": 120, "h": 56 }
    ],

    "tokenMeter": { "x": 1200, "y": 12, "w": 180, "h": 32, "role": "meter" },

    "canvas": { "x": 0, "y": 56, "w": 1140, "h": 844, "role": "main-canvas" },

    "sideMenu": {
      "x": 1140,
      "y": 56,
      "w": 300,
      "h": 844,
      "role": "context-panel",
      "subPanels": {
        "wireframe":   ["boxTools", "tagPicker", "layoutTags"],
        "components":  ["libraryBrowser", "atomToggles"],
        "style":       ["colorPalette", "typographyScale", "spacingControls"],
        "review":      ["issueList", "exportReport"]
      }
    }
  },

  /* =====  Tablet: 834 × 1112  ===== */
  "tablet": {
    "inherit": "desktop",               // start from desktop, override below
    "screen": { "width": 834, "height": 1112 },

    "navBar":  { "w": 834 },
    "canvas":  { "w": 584, "h": 1056 },
    "sideMenu": {
      "x": 584,
      "w": 250,
      "h": 1056
    }
  },

  /* =====  Mobile: 390 × 844 ===== */
  "mobile": {
    "inherit": "desktop",
    "screen": { "width": 390, "height": 844 },

    "navBar":  { "w": 390, "h": 48 },
    "tabs": [
      { "id": "tab-wireframe",  "x": 8,  "y": 0, "w": 88, "h": 48 },
      { "id": "tab-components", "x": 100,"y": 0, "w": 96, "h": 48 },
      { "id": "tab-style",      "x": 198,"y": 0, "w": 70, "h": 48 },
      { "id": "tab-review",     "x": 270,"y": 0, "w": 100,"h": 48 }
    ],

    "tokenMeter": { "x": 300, "y": 8, "w": 80,  "h": 32 },

    "canvas": { "x": 0, "y": 48, "w": 390, "h": 556 },

    /* Contextual panel becomes bottom-sheet on mobile */
    "sideMenu": {
      "x": 0,
      "y": 612,
      "w": 390,
      "h": 232,
      "layout": "bottom-sheet"
    }
  }
}
```

### How to use it

* **Front-end:** Map the `role` props to React components or slots; coordinates convert to CSS grid / flex defaults for each breakpoint.
* **Design hand-off:** Import JSON into Figma via a plugin like *JSON-to-Layers* to auto-generate frames.
* **Further refinement:** Add child elements (toolbar icons, breadcrumb widget, token meter states) in nested objects as you iterate.

Need additional breakpoints, RTL-mirrored coordinates, or a deeper element hierarchy? Just say the word.
