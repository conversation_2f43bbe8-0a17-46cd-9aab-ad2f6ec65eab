# End-to-End Manual Testing Guide

This guide provides step-by-step instructions for manually testing the complete I2D-Convert workflow from image upload through processing to real-time status updates.

## Prerequisites

### 1. Environment Setup
- [ ] Server running on `http://localhost:3001`
- [ ] Client running on `http://localhost:3000`
- [ ] Worker process running (`npm run dev:worker`)
- [ ] Redis server running
- [ ] Database initialized

### 2. Test Images
Prepare test images in different formats and sizes:
- [ ] Valid PNG image (< 10MB)
- [ ] Valid JPEG image (< 10MB)
- [ ] Invalid file type (e.g., .txt, .pdf)
- [ ] Oversized image (> 10MB)
- [ ] Corrupted image file

### 3. Browser Setup
- [ ] Modern browser with developer tools
- [ ] Network tab open for monitoring requests
- [ ] Console tab open for WebSocket logs

## Test Scenarios

### Scenario 1: Complete Happy Path Flow

#### Step 1: User Authentication
1. Navigate to `http://localhost:3000`
2. **Expected**: Login/Register page appears
3. Register a new user or login with existing credentials
4. **Expected**: Successful authentication, redirected to dashboard
5. **Verify**: Auth token stored in browser storage

#### Step 2: WebSocket Connection
1. Open browser developer tools → Console
2. Look for WebSocket connection logs
3. **Expected**: See messages like "✅ WebSocket connected"
4. Navigate to Processing page
5. **Expected**: See "Real-time updates active" indicator

#### Step 3: Image Upload
1. Navigate to Upload page or use upload component
2. Drag and drop a valid PNG/JPEG image (< 10MB)
3. **Expected**: 
   - File validation passes
   - Upload progress bar appears
   - Upload completes successfully
   - Success notification appears
4. **Verify**: 
   - Network tab shows successful POST to `/api/v1/images/upload`
   - Response includes image ID and processing job ID

#### Step 4: Real-time Processing Updates
1. Immediately navigate to Processing page
2. **Expected**: 
   - New job appears in the list
   - Status shows "QUEUED" or "PROCESSING"
   - Progress bar is visible and updating
   - Real-time updates indicator shows "active"
3. **Monitor**: 
   - Console shows WebSocket progress messages
   - Progress bar updates smoothly (10% → 25% → 50% → 75% → 100%)
   - Status changes from QUEUED → PROCESSING → COMPLETED
4. **Verify**: 
   - Job completes within reasonable time (< 30 seconds)
   - Final status shows "COMPLETED"
   - Processing results are displayed

#### Step 5: Data Persistence
1. Refresh the Processing page
2. **Expected**: 
   - Completed job still appears in list
   - Status remains "COMPLETED"
   - Results are preserved
3. Navigate away and back to Processing page
4. **Expected**: Data persists across navigation

### Scenario 2: Error Handling

#### Test 2.1: Invalid File Upload
1. Try to upload a text file (.txt)
2. **Expected**: 
   - Validation error appears
   - File is rejected before upload
   - Error message: "Invalid file type"

#### Test 2.2: Oversized File Upload
1. Try to upload an image > 10MB
2. **Expected**: 
   - Validation error appears
   - File is rejected before upload
   - Error message: "File size exceeds 10MB limit"

#### Test 2.3: Network Disconnection
1. Start an image upload
2. Disconnect network during upload
3. **Expected**: 
   - Upload fails gracefully
   - Error notification appears
   - User can retry upload

#### Test 2.4: WebSocket Disconnection
1. Start processing an image
2. Simulate WebSocket disconnection (close browser tab briefly)
3. **Expected**: 
   - Real-time updates indicator shows "disconnected"
   - Page still functions (can refresh for updates)
   - WebSocket reconnects automatically

### Scenario 3: Multiple Concurrent Jobs

#### Test 3.1: Batch Upload
1. Upload 3-5 images simultaneously
2. **Expected**: 
   - All uploads succeed
   - Multiple jobs appear in Processing page
   - Each job has independent progress tracking
   - All jobs complete successfully

#### Test 3.2: Queue Management
1. Upload multiple large images
2. Monitor Processing page
3. **Expected**: 
   - Jobs are queued appropriately
   - Processing happens in order
   - No jobs are lost or stuck

### Scenario 4: User Experience

#### Test 4.1: Responsive Design
1. Test on different screen sizes
2. **Expected**: 
   - Upload interface works on mobile
   - Processing page is readable on all devices
   - Progress bars scale appropriately

#### Test 4.2: Performance
1. Upload and process multiple images
2. Monitor browser performance
3. **Expected**: 
   - Page remains responsive
   - Memory usage stays reasonable
   - No memory leaks from WebSocket connections

## Verification Checklist

### Backend Verification
- [ ] Database contains image records
- [ ] Processing jobs are created and updated
- [ ] S3/Storage contains uploaded files
- [ ] Queue system processes jobs correctly
- [ ] WebSocket events are broadcast properly

### Frontend Verification
- [ ] File upload UI works correctly
- [ ] Progress tracking is accurate
- [ ] Real-time updates function properly
- [ ] Error states are handled gracefully
- [ ] Navigation preserves state

### Integration Verification
- [ ] Authentication flow works end-to-end
- [ ] File upload triggers processing pipeline
- [ ] WebSocket updates match job progress
- [ ] Database state reflects UI state
- [ ] Error handling works across all layers

## Performance Benchmarks

### Expected Performance
- **Upload Time**: < 5 seconds for 5MB image
- **Processing Time**: < 30 seconds for typical image
- **WebSocket Latency**: < 100ms for progress updates
- **Page Load Time**: < 2 seconds
- **Memory Usage**: < 100MB for typical session

### Load Testing
1. Upload 10 images simultaneously
2. **Expected**: 
   - All uploads succeed
   - Processing completes within 5 minutes
   - No server errors or timeouts
   - WebSocket connections remain stable

## Troubleshooting

### Common Issues

#### WebSocket Not Connecting
- Check server is running on correct port
- Verify CORS settings
- Check browser console for connection errors

#### Upload Failing
- Verify file size and type
- Check authentication token
- Monitor network requests for errors

#### Processing Stuck
- Check worker process is running
- Verify Redis connection
- Check server logs for errors

#### Real-time Updates Not Working
- Verify WebSocket connection
- Check job room joining
- Monitor console for progress events

## Test Data Cleanup

After testing:
- [ ] Remove test images from storage
- [ ] Clean up test user data
- [ ] Clear test processing jobs
- [ ] Reset database to clean state

## Automated Test Integration

Run automated tests alongside manual testing:

```bash
# Run end-to-end tests
npm run test:e2e

# Run with coverage
npm run test:coverage

# Run specific test suite
npm run test tests/e2e/end-to-end-flow.test.ts
```

## Success Criteria

The end-to-end flow is considered successful when:

1. ✅ Users can upload images without errors
2. ✅ Processing jobs are created and executed
3. ✅ Real-time updates work consistently
4. ✅ Data persists correctly across sessions
5. ✅ Error scenarios are handled gracefully
6. ✅ Performance meets expected benchmarks
7. ✅ Multiple concurrent operations work correctly
8. ✅ User experience is smooth and intuitive
