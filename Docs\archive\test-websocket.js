const { io } = require('socket.io-client');

console.log('🔌 Testing WebSocket connection...');

const socket = io('http://localhost:3001', {
  path: '/ws/socket.io/',
  transports: ['websocket', 'polling'],
});

socket.on('connect', () => {
  console.log('✅ Connected to WebSocket server');
  console.log('Socket ID:', socket.id);
  
  // Test joining a job room
  socket.emit('join-job', 'test-job-123');
  console.log('📋 Joined test job room');
  
  // Disconnect after 5 seconds
  setTimeout(() => {
    console.log('🔌 Disconnecting...');
    socket.disconnect();
  }, 5000);
});

socket.on('disconnect', (reason) => {
  console.log('❌ Disconnected:', reason);
  process.exit(0);
});

socket.on('connect_error', (error) => {
  console.error('🔌 Connection error:', error);
  process.exit(1);
});

socket.on('job-progress', (data) => {
  console.log('📊 Job progress received:', data);
});

console.log('🔌 Attempting to connect...');
