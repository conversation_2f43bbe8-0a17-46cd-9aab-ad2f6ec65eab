const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001/api/v1';

async function testDatabase() {
  console.log('🗄️ Testing Database Operations...\n');
  
  const testEmail = `dbtest${Date.now()}@example.com`;
  let authToken = '';
  let userId = '';
  let projectId = '';

  try {
    // Step 1: Register a test user
    console.log('1. Creating test user...');
    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: 'testpassword123',
        name: 'Database Test User'
      })
    });
    
    const registerData = await registerResponse.json();
    if (registerData.success) {
      authToken = registerData.data.token;
      userId = registerData.data.user.id;
      console.log('✅ Test user created successfully');
    } else {
      throw new Error(`Registration failed: ${registerData.error}`);
    }

    // Step 2: Test project creation
    console.log('\n2. Testing project creation...');
    const createProjectResponse = await fetch(`${BASE_URL}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Test Project',
        description: 'A test project for database validation',
        settings: { theme: 'dark', autoProcess: true }
      })
    });
    
    const projectData = await createProjectResponse.json();
    if (projectData.success) {
      projectId = projectData.data.id;
      console.log('✅ Project created successfully');
      console.log('   Project ID:', projectId);
    } else {
      console.log('❌ Project creation failed:', projectData.error);
    }

    // Step 3: Test getting user projects
    console.log('\n3. Testing project retrieval...');
    const getProjectsResponse = await fetch(`${BASE_URL}/projects`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    const projectsData = await getProjectsResponse.json();
    if (projectsData.success) {
      console.log('✅ Projects retrieved successfully');
      console.log('   Project count:', projectsData.data.projects.length);
    } else {
      console.log('❌ Project retrieval failed:', projectsData.error);
    }

    // Step 4: Test project update
    console.log('\n4. Testing project update...');
    const updateProjectResponse = await fetch(`${BASE_URL}/projects/${projectId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: 'Updated Test Project',
        description: 'Updated description'
      })
    });
    
    const updateData = await updateProjectResponse.json();
    if (updateData.success) {
      console.log('✅ Project updated successfully');
    } else {
      console.log('❌ Project update failed:', updateData.error);
    }

    // Step 5: Test user preferences update
    console.log('\n5. Testing user preferences update...');
    const updatePrefsResponse = await fetch(`${BASE_URL}/users/me/preferences`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        theme: 'dark',
        language: 'en',
        notifications: {
          email: true,
          push: false,
          processing: true
        }
      })
    });
    
    const prefsData = await updatePrefsResponse.json();
    if (prefsData.success) {
      console.log('✅ User preferences updated successfully');
    } else {
      console.log('❌ User preferences update failed:', prefsData.error);
    }

    // Step 6: Test token transactions
    console.log('\n6. Testing token data retrieval...');
    const tokenResponse = await fetch(`${BASE_URL}/users/me/tokens`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    const tokenData = await tokenResponse.json();
    if (tokenData.success) {
      console.log('✅ Token data retrieved successfully');
      console.log('   Current balance:', tokenData.data.currentBalance);
      console.log('   Transaction count:', tokenData.data.transactions.length);
    } else {
      console.log('❌ Token data retrieval failed:', tokenData.error);
    }

    // Step 7: Test user activity log
    console.log('\n7. Testing user activity log...');
    const activityResponse = await fetch(`${BASE_URL}/users/me/activity`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    const activityData = await activityResponse.json();
    if (activityData.success) {
      console.log('✅ User activity retrieved successfully');
      console.log('   Activity count:', activityData.data.activities.length);
    } else {
      console.log('❌ User activity retrieval failed:', activityData.error);
    }

    // Step 8: Test data validation (invalid data)
    console.log('\n8. Testing data validation...');
    const invalidProjectResponse = await fetch(`${BASE_URL}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        name: '', // Invalid: empty name
        description: 'A'.repeat(600) // Invalid: too long
      })
    });
    
    const invalidData = await invalidProjectResponse.json();
    if (!invalidData.success && invalidData.error) {
      console.log('✅ Data validation working correctly');
      console.log('   Validation error caught:', invalidData.error);
    } else {
      console.log('❌ Data validation failed - invalid data was accepted');
    }

    console.log('\n🎉 Database testing completed successfully!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

testDatabase();
