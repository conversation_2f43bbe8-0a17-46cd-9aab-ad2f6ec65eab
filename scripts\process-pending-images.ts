import dotenv from 'dotenv';
dotenv.config();

import { PrismaClient } from '@prisma/client';

async function processPendingImages() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Finding pending images...\n');

    // Find all images with PENDING status
    const pendingImages = await prisma.image.findMany({
      where: {
        processingStatus: 'PENDING'
      },
      select: {
        id: true,
        userId: true,
        filename: true,
        s3Key: true,
        s3Bucket: true,
        mode: true,
        createdAt: true
      }
    });

    console.log(`📸 Found ${pendingImages.length} pending images:`);
    pendingImages.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.filename} (${image.id})`);
    });

    if (pendingImages.length === 0) {
      console.log('✅ No pending images found.');
      return;
    }

    // Import services after dotenv is loaded
    const { QueueService } = await import('../src/server/services/queue');
    const { RedisService } = await import('../src/server/services/redis');
    const { DatabaseService } = await import('../src/server/services/database');

    // Initialize services
    await RedisService.initialize();
    await QueueService.initialize('producer');
    await DatabaseService.initialize();

    console.log('\n🚀 Processing pending images...\n');

    for (const image of pendingImages) {
      try {
        console.log(`📋 Processing image: ${image.filename}`);

        // Create processing job record in database
        const processingJob = await DatabaseService.createProcessingJob({
          userId: image.userId,
          imageId: image.id,
          type: 'IMAGE_PROCESSING',
          priority: 1,
          input: {
            s3Key: image.s3Key,
            s3Bucket: image.s3Bucket,
            mode: image.mode,
            settings: {}
          }
        });
        console.log(`  ✓ Processing job record created: ${processingJob.id}`);

        // Create Bull queue job (using standard processing)
        await QueueService.addImageProcessingJob({
          imageId: image.id,
          userId: image.userId,
          s3Key: image.s3Key,
          s3Bucket: image.s3Bucket,
          mode: image.mode,
          settings: {
            processingType: 'standard'
          }
        });
        console.log(`  ✓ Queue job created for image ${image.id}`);

        // Update processing job status to RUNNING
        await DatabaseService.updateProcessingJob(processingJob.id, {
          status: 'RUNNING',
          startedAt: new Date()
        });
        console.log(`  ✓ Processing job status updated to RUNNING`);

        // Update image status to PROCESSING
        await prisma.image.update({
          where: { id: image.id },
          data: { processingStatus: 'PROCESSING' }
        });
        console.log(`  ✓ Image status updated to PROCESSING\n`);

      } catch (error) {
        console.error(`  ❌ Failed to process image ${image.filename}:`, error);
      }
    }

    console.log('✅ Finished processing pending images');

  } catch (error) {
    console.error('❌ Error processing pending images:', error);
  } finally {
    await prisma.$disconnect();
    process.exit(0);
  }
}

processPendingImages();
