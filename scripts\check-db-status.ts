import dotenv from 'dotenv';
dotenv.config();

import { PrismaClient } from '@prisma/client';

async function checkDatabaseStatus() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking database status...\n');

    // Check images
    const images = await prisma.image.findMany({
      select: {
        id: true,
        filename: true,
        processingStatus: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📸 Images in database (${images.length}):`);
    images.forEach((image, index) => {
      console.log(`  ${index + 1}. ${image.filename} - Status: ${image.processingStatus} - Created: ${image.createdAt.toISOString()}`);
    });

    // Check processing jobs
    const processingJobs = await prisma.processingJob.findMany({
      select: {
        id: true,
        imageId: true,
        status: true,
        type: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`\n⚙️ Processing Jobs in database (${processingJobs.length}):`);
    processingJobs.forEach((job, index) => {
      console.log(`  ${index + 1}. Job ID: ${job.id} - Image: ${job.imageId} - Status: ${job.status} - Type: ${job.type}`);
    });

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabaseStatus();
