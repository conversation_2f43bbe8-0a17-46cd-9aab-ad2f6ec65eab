# Feature Specification: Image Ingestion & Processing Pipeline

**Document Version:** 1.0  
**Date:** 31 July 2025  
**Feature Priority:** P0 (Critical for Beta Release)  
**Target Release:** Q3 2025 Beta  
**Estimated Effort:** 3-4 weeks (2 developers)

---

## 1. Feature Overview

### 1.1 Purpose
The Image Ingestion & Processing Pipeline is the foundational entry point for the I2D-Convert system. It handles secure upload, validation, preprocessing, and storage of UI images/screenshots that will be processed by the  vision pipeline.

### 1.2 Success Criteria
- Support PNG/JPG uploads up to 10MB
- Process images ≤1080p within 2 seconds
- 99.9% upload success rate
- Automatic detection of manual-mode triggers (`_ui` filename)
- Secure storage with GDPR compliance
- Integration-ready for  pipeline consumption

### 1.3 User Stories
- **As a designer**, I want to upload UI screenshots quickly so I can start the conversion process
- **As a developer**, I want reliable image preprocessing so the  pipeline receives consistent input
- **As a product owner**, I want usage tracking so we can monitor token consumption

---

## 2. Technical Architecture

### 2.1 System Components

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐    ┌──────────────┐
│   Client    │───▶│  Upload API  │───▶│ Processing  │───▶│   Storage    │
│  (Browser)  │    │   Gateway    │    │   Service   │    │   Service    │
└─────────────┘    └──────────────┘    └─────────────┘    └──────────────┘
                           │                    │                 │
                           ▼                    ▼                 ▼
                   ┌──────────────┐    ┌─────────────┐    ┌──────────────┐
                   │ Validation   │    │ Metadata    │    │   Queue   │
                   │   Service    │    │ Extraction  │    │   Service    │
                   └──────────────┘    └─────────────┘    └──────────────┘
```

### 2.2 Technology Stack
- **Frontend**: React with react-dropzone for drag-and-drop
- **Backend**: Node.js/Express with multer for file handling
- **Storage**: AWS S3 with CloudFront CDN
- **Processing**: Sharp.js for image manipulation
- **Queue**: Redis for  pipeline integration
- **Database**: PostgreSQL for metadata storage

### 2.3 Data Flow
1. User selects/drags image file(s)
2. Client-side validation (file type, size)
3. Secure upload to API gateway
4. Server-side validation and virus scanning
5. Image preprocessing (resize, normalize, extract metadata)
6. Storage in S3 with generated UUID
7. Metadata persistence in PostgreSQL
8. Queue job for  pipeline processing
9. Real-time status updates to client

---

## 3. API Specifications

### 3.1 Upload Endpoint

```typescript
POST /api/v1/images/upload
Content-Type: multipart/form-data

Request:
{
  file: File,           // Image file (PNG/JPG)
  projectId?: string,   // Optional project association
  mode?: 'auto' | 'manual'  // Processing mode override
}

Response (Success - 201):
{
  success: true,
  data: {
    imageId: string,
    filename: string,
    size: number,
    dimensions: { width: number, height: number },
    mode: 'auto' | 'manual',
    uploadedAt: string,
    processingStatus: 'queued' | 'processing' | 'completed' | 'failed',
    previewUrl: string,
    estimatedTokens: number
  }
}

Response (Error - 400/413/422):
{
  success: false,
  error: {
    code: string,
    message: string,
    details?: object
  }
}
```

### 3.2 Status Endpoint

```typescript
GET /api/v1/images/{imageId}/status

Response:
{
  success: true,
  data: {
    imageId: string,
    processingStatus: 'queued' | 'processing' | 'completed' | 'failed',
    progress: number,        // 0-100
    estimatedTimeRemaining: number,  // seconds
    errorMessage?: string,
    results?: {
      segmentationReady: boolean,
      metadataExtracted: boolean,
      QueuePosition?: number
    }
  }
}
```

---

## 4. Data Models

### 4.1 Image Record

```typescript
interface ImageRecord {
  id: string;              // UUID
  userId: string;
  projectId?: string;
  filename: string;
  originalFilename: string;
  mimeType: string;
  size: number;            // bytes
  dimensions: {
    width: number;
    height: number;
  };
  mode: 'auto' | 'manual';
  processingStatus: ProcessingStatus;
  s3Key: string;
  s3Bucket: string;
  previewUrl: string;
  metadata: ImageMetadata;
  tokenCost: number;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
}

interface ImageMetadata {
  colorProfile: string;
  hasTransparency: boolean;
  dominantColors: string[];
  estimatedComplexity: 'low' | 'medium' | 'high';
  detectedElements: number;
  isScreenshot: boolean;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}

type ProcessingStatus = 
  | 'uploading'
  | 'validating' 
  | 'preprocessing'
  | 'queued'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';
```

---

## 5. Security & Validation

### 5.1 File Validation Rules
- **Allowed formats**: PNG, JPG, JPEG only
- **Max file size**: 10MB
- **Max dimensions**: 4K (3840×2160)
- **Min dimensions**: 100×100 pixels
- **Virus scanning**: ClamAV integration
- **Content validation**: Magic number verification

### 5.2 Security Measures
- **Upload rate limiting**: 10 files per minute per user
- **CSRF protection**: Double-submit cookie pattern
- **File sanitization**: Strip EXIF data, rename files
- **Access control**: Signed URLs with 1-hour expiration
- **Encryption**: AES-256 at rest, TLS 1.3 in transit

---

## 6. Performance Requirements

### 6.1 Latency Targets
- **Upload initiation**: <500ms
- **File validation**: <1s
- **Image preprocessing**: <2s for ≤1080p
- **Metadata extraction**: <1s
- **Queue submission**: <500ms

### 6.2 Throughput Targets
- **Concurrent uploads**: 100 per server instance
- **Daily volume**: 10,000 images (Beta target)
- **Peak load**: 50 uploads/second

### 6.3 Scalability
- **Horizontal scaling**: Auto-scaling groups
- **CDN integration**: CloudFront for global delivery
- **Database optimization**: Read replicas for metadata queries

---

## 7. Integration Points

### 7.1 Pipeline Integration
```typescript
interface QueueJob {
  imageId: string;
  s3Key: string;
  mode: 'auto' | 'manual';
  priority: number;
  userId: string;
  tokenBudget: number;
  preprocessingResults: {
    normalizedUrl: string;
    thumbnailUrl: string;
    metadata: ImageMetadata;
  };
}
```

### 7.2 Token Service Integration
- Deduct 1 token for upload processing
- Reserve estimated tokens for  processing
- Handle insufficient balance scenarios

### 7.3 UI Integration
- WebSocket connection for real-time status updates
- Progress indicators with estimated completion time
- Error handling with user-friendly messages

---

## 8. Error Handling

### 8.1 Error Categories
- **Client errors**: Invalid file type, size exceeded, network issues
- **Server errors**: Processing failures, storage issues, service unavailable
- **Business logic errors**: Insufficient tokens, rate limiting, quota exceeded

### 8.2 Recovery Strategies
- **Automatic retry**: Transient failures with exponential backoff
- **Graceful degradation**: Fallback to basic processing if advanced features fail
- **User notification**: Clear error messages with suggested actions

---

## 9. Testing Strategy

### 9.1 Unit Tests
- File validation logic
- Image preprocessing functions
- Metadata extraction accuracy
- Error handling scenarios

### 9.2 Integration Tests
- End-to-end upload flow
- S3 storage integration
- Queue service integration
- Database persistence

### 9.3 Performance Tests
- Load testing with concurrent uploads
- Large file handling
- Memory usage optimization
- CDN cache effectiveness

---

## 10. Monitoring & Observability

### 10.1 Metrics
- Upload success/failure rates
- Processing latency percentiles
- Storage utilization
- Token consumption patterns

### 10.2 Alerts
- High error rates (>5%)
- Processing delays (>10s)
- Storage quota warnings
- Service health checks

---

## 11. Implementation Roadmap

### Phase 1: Core Upload (Week 1-2)
- Basic file upload API
- Client-side validation
- S3 storage integration
- Database schema setup

### Phase 2: Processing Pipeline (Week 2-3)
- Image preprocessing service
- Metadata extraction
- Queue integration
- Status tracking

### Phase 3: Security & Performance (Week 3-4)
- Security hardening
- Performance optimization
- Monitoring setup
- Load testing

### Phase 4: Integration & Polish (Week 4)
-  pipeline integration
- UI/UX refinements
- Documentation
- Beta deployment

---

## 12. Dependencies & Risks

### 12.1 External Dependencies
- S3 service availability
- Redis queue service
- PostgreSQL database
- ClamAV virus scanning

### 12.2 Technical Risks
- **High**:  pipeline integration complexity
- **Medium**: Performance under load
- **Low**: File format compatibility issues

### 12.3 Mitigation Strategies
- Comprehensive testing environment
- Gradual rollout with feature flags
- Fallback mechanisms for critical paths
- Regular security audits

---

*This specification serves as the foundation for implementing the Image Ingestion & Processing Pipeline. All implementation should follow the technical standards defined in the project's Agent OS configuration.*
