#!/usr/bin/env tsx

/**
 * End-to-End Flow Test Script
 * 
 * This script tests the complete image upload and processing workflow:
 * 1. Fresh image upload
 * 2. Job creation and status tracking
 * 3. Status badge display verification
 * 4. Real-time WebSocket updates
 * 5. Job completion workflow
 * 6. Error handling scenarios
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';
import { io, Socket } from 'socket.io-client';

// Configuration
const API_BASE_URL = 'http://localhost:3001/api/v1';
const WS_URL = 'http://localhost:3001';
const TEST_IMAGE_PATH = path.join(__dirname, 'test-image.png');

interface TestResults {
  uploadSuccess: boolean;
  jobCreated: boolean;
  statusTracking: boolean;
  websocketUpdates: boolean;
  jobCompletion: boolean;
  errorHandling: boolean;
  errors: string[];
}

class EndToEndTester {
  private authToken: string = '';
  private socket: Socket | null = null;
  private results: TestResults = {
    uploadSuccess: false,
    jobCreated: false,
    statusTracking: false,
    websocketUpdates: false,
    jobCompletion: false,
    errorHandling: false,
    errors: []
  };

  async run(): Promise<void> {
    console.log('🚀 Starting End-to-End Flow Test...\n');

    try {
      // Step 1: Setup
      await this.setup();
      
      // Step 2: Test fresh image upload
      await this.testImageUpload();
      
      // Step 3: Test job creation and status tracking
      await this.testJobCreationAndTracking();
      
      // Step 4: Test WebSocket updates
      await this.testWebSocketUpdates();
      
      // Step 5: Test job completion workflow
      await this.testJobCompletion();
      
      // Step 6: Test error handling
      await this.testErrorHandling();
      
      // Step 7: Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      this.results.errors.push(`Test execution failed: ${error}`);
    } finally {
      await this.cleanup();
    }
  }

  private async setup(): Promise<void> {
    console.log('📋 Setting up test environment...');
    
    // Create test image
    await this.createTestImage();
    
    // Register/login test user
    await this.authenticateTestUser();
    
    // Setup WebSocket connection
    await this.setupWebSocket();
    
    console.log('✅ Setup completed\n');
  }

  private async createTestImage(): Promise<void> {
    // Create a simple 100x100 PNG test image
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
      0x00, 0x00, 0x00, 0x64, 0x00, 0x00, 0x00, 0x64, // 100x100 dimensions
      0x08, 0x02, 0x00, 0x00, 0x00, 0xFF, 0x80, 0x02, // bit depth, color type, etc.
      0x03, 0x00, 0x00, 0x00, 0x0C, 0x49, 0x44, 0x41, // IDAT chunk
      0x54, 0x08, 0x99, 0x01, 0x01, 0x01, 0x00, 0x00, // image data
      0xFE, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, // more data
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, // IEND chunk
      0xAE, 0x42, 0x60, 0x82
    ]);

    fs.writeFileSync(TEST_IMAGE_PATH, testImageBuffer);
    console.log(`📸 Test image created: ${TEST_IMAGE_PATH}`);
  }

  private async authenticateTestUser(): Promise<void> {
    const timestamp = Date.now();
    const testUser = {
      email: `e2e-test-${timestamp}@example.com`,
      password: 'TestPassword123!',
      name: 'E2E Test User'
    };

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, testUser);
      this.authToken = response.data.token;
      console.log('🔐 Test user authenticated');
    } catch (error: any) {
      if (error.response?.status === 429) {
        // Rate limited, try to login instead
        const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });
        this.authToken = loginResponse.data.token;
        console.log('🔐 Test user logged in (rate limited)');
      } else {
        throw new Error(`Authentication failed: ${error.message}`);
      }
    }
  }

  private async setupWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.socket = io(WS_URL, {
        auth: { token: this.authToken },
        transports: ['websocket']
      });

      this.socket.on('connect', () => {
        console.log('🔌 WebSocket connected');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection failed:', error);
        reject(error);
      });

      // Set timeout for connection
      setTimeout(() => {
        if (!this.socket?.connected) {
          reject(new Error('WebSocket connection timeout'));
        }
      }, 5000);
    });
  }

  private async testImageUpload(): Promise<void> {
    console.log('📤 Testing fresh image upload...');
    
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(TEST_IMAGE_PATH));
      formData.append('mode', 'ui');
      formData.append('processingType', 'multi-stage');

      const response = await axios.post(`${API_BASE_URL}/images/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          ...formData.getHeaders()
        }
      });

      if (response.status === 200 && response.data.success) {
        this.results.uploadSuccess = true;
        console.log('✅ Image upload successful');
        console.log(`   Image ID: ${response.data.data.image.id}`);
        console.log(`   Job ID: ${response.data.data.processingJob.id}`);
        
        // Store IDs for further testing
        (this as any).imageId = response.data.data.image.id;
        (this as any).jobId = response.data.data.processingJob.id;
      } else {
        throw new Error(`Upload failed: ${response.data.message || 'Unknown error'}`);
      }
    } catch (error: any) {
      this.results.errors.push(`Image upload failed: ${error.message}`);
      console.error('❌ Image upload failed:', error.message);
    }
  }

  private async testJobCreationAndTracking(): Promise<void> {
    console.log('📋 Testing job creation and status tracking...');
    
    if (!(this as any).jobId) {
      this.results.errors.push('No job ID available for tracking test');
      return;
    }

    try {
      // Test job status endpoint
      const response = await axios.get(`${API_BASE_URL}/processing/jobs/${(this as any).jobId}`, {
        headers: { 'Authorization': `Bearer ${this.authToken}` }
      });

      if (response.status === 200 && response.data.success) {
        const job = response.data.data;
        console.log(`✅ Job tracking successful - Status: ${job.status}`);
        console.log(`   Progress: ${Math.round((job.progress || 0) * 100)}%`);
        this.results.jobCreated = true;
        this.results.statusTracking = true;
      } else {
        throw new Error('Job status check failed');
      }
    } catch (error: any) {
      this.results.errors.push(`Job tracking failed: ${error.message}`);
      console.error('❌ Job tracking failed:', error.message);
    }
  }

  private async testWebSocketUpdates(): Promise<void> {
    console.log('🔌 Testing WebSocket updates...');
    
    if (!this.socket || !(this as any).jobId) {
      this.results.errors.push('WebSocket or job ID not available for testing');
      return;
    }

    return new Promise((resolve) => {
      let updateReceived = false;
      
      // Join job room
      this.socket!.emit('join-job', (this as any).jobId);
      console.log(`📡 Joined job room: ${(this as any).jobId}`);

      // Listen for job progress updates
      this.socket!.on('job-progress', (data) => {
        console.log(`📊 WebSocket update received: ${data.progress}% - ${data.status}`);
        updateReceived = true;
        this.results.websocketUpdates = true;
      });

      // Wait for updates or timeout
      setTimeout(() => {
        if (!updateReceived) {
          this.results.errors.push('No WebSocket updates received within timeout');
          console.log('⚠️  No WebSocket updates received within 10 seconds');
        }
        resolve();
      }, 10000);
    });
  }

  private async testJobCompletion(): Promise<void> {
    console.log('⏳ Testing job completion workflow...');
    
    if (!(this as any).jobId) {
      this.results.errors.push('No job ID available for completion test');
      return;
    }

    // Wait for job completion (up to 60 seconds)
    let attempts = 0;
    const maxAttempts = 60;
    
    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(`${API_BASE_URL}/processing/jobs/${(this as any).jobId}`, {
          headers: { 'Authorization': `Bearer ${this.authToken}` }
        });

        if (response.data.success) {
          const job = response.data.data;
          console.log(`📊 Job status: ${job.status} (${Math.round((job.progress || 0) * 100)}%)`);
          
          if (job.status === 'COMPLETED') {
            this.results.jobCompletion = true;
            console.log('✅ Job completed successfully');
            console.log(`   Processing time: ${job.processingTime || 'N/A'}ms`);
            break;
          } else if (job.status === 'FAILED') {
            this.results.errors.push(`Job failed: ${job.error || 'Unknown error'}`);
            console.error('❌ Job failed:', job.error);
            break;
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      } catch (error: any) {
        this.results.errors.push(`Job completion check failed: ${error.message}`);
        break;
      }
    }

    if (attempts >= maxAttempts) {
      this.results.errors.push('Job completion timeout (60 seconds)');
      console.log('⚠️  Job completion timeout');
    }
  }

  private async testErrorHandling(): Promise<void> {
    console.log('🚨 Testing error handling scenarios...');
    
    try {
      // Test invalid file upload
      const formData = new FormData();
      formData.append('file', Buffer.from('invalid image data'), 'test.txt');
      formData.append('mode', 'ui');

      const response = await axios.post(`${API_BASE_URL}/images/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          ...formData.getHeaders()
        },
        validateStatus: () => true // Don't throw on error status
      });

      if (response.status >= 400) {
        this.results.errorHandling = true;
        console.log('✅ Error handling working - Invalid file rejected');
      } else {
        this.results.errors.push('Error handling failed - Invalid file was accepted');
      }
    } catch (error: any) {
      this.results.errors.push(`Error handling test failed: ${error.message}`);
    }
  }

  private generateReport(): void {
    console.log('\n📊 End-to-End Test Report');
    console.log('=' .repeat(50));
    
    const tests = [
      { name: 'Image Upload', passed: this.results.uploadSuccess },
      { name: 'Job Creation', passed: this.results.jobCreated },
      { name: 'Status Tracking', passed: this.results.statusTracking },
      { name: 'WebSocket Updates', passed: this.results.websocketUpdates },
      { name: 'Job Completion', passed: this.results.jobCompletion },
      { name: 'Error Handling', passed: this.results.errorHandling }
    ];

    tests.forEach(test => {
      const status = test.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${test.name.padEnd(20)} ${status}`);
    });

    const passedTests = tests.filter(t => t.passed).length;
    const totalTests = tests.length;
    
    console.log('\n📈 Summary:');
    console.log(`Tests Passed: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
    
    if (this.results.errors.length > 0) {
      console.log('\n🚨 Errors:');
      this.results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
  }

  private async cleanup(): Promise<void> {
    console.log('\n🧹 Cleaning up...');
    
    if (this.socket) {
      this.socket.disconnect();
      console.log('🔌 WebSocket disconnected');
    }
    
    if (fs.existsSync(TEST_IMAGE_PATH)) {
      fs.unlinkSync(TEST_IMAGE_PATH);
      console.log('🗑️  Test image deleted');
    }
    
    console.log('✅ Cleanup completed');
  }
}

// Run the test
if (require.main === module) {
  const tester = new EndToEndTester();
  tester.run().catch(console.error);
}

export default EndToEndTester;
