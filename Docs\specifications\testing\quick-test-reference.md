# 🚀 Quick Test Reference Card
## I2D-Convert Testing Commands & Checklist

---

## ⚡ Quick Commands

### Start Services
```bash
npm run dev:services          # Start all services
npm run validate-env          # Check environment health
```

### Run Tests
```bash
npm run test:smoke           # 30s - Basic functionality
npm run test:e2e             # 5min - Complete workflow  
npm run test:performance     # 2min - Load testing
npm run test:e2e-full        # 15min - Full automated suite
```

### Debug & Logs
```bash
docker-compose logs backend  # Server logs
docker-compose logs worker   # Worker logs
DEBUG=* npm run dev          # Debug mode
```

---

## ✅ 30-Second Health Check

1. **Start Services**: `npm run dev:services`
2. **Run Smoke Tests**: `npm run test:smoke`
3. **Check Results**: Look for 7-8 passing tests

**Expected Output:**
```
✓ Server Health > should respond to health check
✓ WebSocket Server > should accept WebSocket connections  
✓ Database Connectivity > should connect to database
```

---

## 🧪 Test Types & Expected Results

| Test Type | Command | Duration | Success Criteria |
|-----------|---------|----------|------------------|
| **Smoke** | `test:smoke` | 30s | 7-8 tests pass |
| **E2E** | `test:e2e` | 5min | Auth + upload flow works |
| **Performance** | `test:performance` | 2min | WebSocket 100% success |
| **Manual** | Browser testing | 15min | Complete user journey |

---

## 🔍 Manual Testing Checklist

### Core User Flow (5 minutes)
- [ ] **Navigate**: Open `http://localhost:3000`
- [ ] **Register**: Create account with unique email
- [ ] **Login**: Sign in with credentials  
- [ ] **Upload**: Drag & drop test image
- [ ] **Monitor**: Watch real-time progress
- [ ] **Interact**: Test canvas zoom/pan
- [ ] **Export**: Download results

### Quick Validation Points
- [ ] **Responsive**: Test mobile/tablet view
- [ ] **WebSocket**: Real-time updates working
- [ ] **Performance**: Smooth 60 FPS canvas
- [ ] **Security**: Rate limiting active (429 errors OK)

---

## 🚨 Common Issues & Quick Fixes

### Services Won't Start
```bash
docker-compose down
docker-compose up -d
npm run validate-env
```

### Rate Limiting Errors (429)
- **Expected behavior** - security working correctly
- Wait 15 minutes or use different email/IP

### WebSocket Connection Failed
```bash
npm run dev:restart
# Check port 3001 is available
```

### Database Issues
```bash
npm run db:reset
npm run db:migrate
```

---

## 📊 Performance Benchmarks

### Excellent Performance ✅
- WebSocket latency: < 50ms
- Memory usage: < 100MB  
- Upload speed: > 1MB/s
- Canvas FPS: 60 FPS

### Acceptable Performance ⚠️
- WebSocket latency: < 100ms
- Memory usage: < 500MB
- Upload speed: > 500KB/s  
- Canvas FPS: > 30 FPS

### Performance Issues ❌
- WebSocket latency: > 200ms
- Memory usage: > 1GB
- Upload speed: < 100KB/s
- Canvas FPS: < 15 FPS

---

## 🔒 Security Test Points

### Authentication
- [ ] Password strength enforced
- [ ] JWT tokens expire properly
- [ ] Rate limiting active (5 req/15min)
- [ ] Unauthorized access blocked

### File Upload
- [ ] File type validation working
- [ ] Size limits enforced
- [ ] Malicious files rejected
- [ ] Proper error messages

---

## 🌐 API Quick Tests

### Health Check
```bash
curl http://localhost:3001/api/health
# Expected: {"status":"ok"}
```

### Auth Test
```bash
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}'
```

### WebSocket Test (Browser Console)
```javascript
const socket = io('http://localhost:3001');
socket.on('connect', () => console.log('✅ Connected'));
```

---

## 🎯 Success Indicators

### Automated Tests
```
✓ 7-8 smoke tests passing
✓ WebSocket connections: 100% success rate  
✓ Memory usage: < 200% increase under load
✓ Rate limiting: 429 errors (expected)
```

### Manual Tests
```
✓ User registration: < 30s
✓ Image upload: < 60s for 5MB
✓ Canvas interaction: Smooth performance
✓ Export generation: < 30s
```

---

## 📱 Mobile Testing

### Quick Mobile Check
1. Open Chrome DevTools
2. Toggle device toolbar (Ctrl+Shift+M)
3. Test iPhone/Android views
4. Verify touch gestures work
5. Check responsive layout

---

## 🔧 Debug Mode

### Enable Detailed Logging
```bash
DEBUG=* npm run dev
```

### Check Specific Logs
```bash
DEBUG=socket.io* npm run dev     # WebSocket logs
DEBUG=express* npm run dev       # API logs  
DEBUG=prisma* npm run dev        # Database logs
```

---

## 📋 Test Report Template

```markdown
## Quick Test Report - $(date)

### Automated Results
- Smoke Tests: ✅/❌ (X/8 passed)
- Performance: ✅/❌ (WebSocket success rate: X%)
- E2E Tests: ✅/❌ (Complete workflow: working/failed)

### Manual Validation  
- User Flow: ✅/❌ (Registration → Upload → Export)
- Performance: ✅/❌ (Canvas FPS: X, Upload speed: X)
- Mobile: ✅/❌ (Responsive design working)

### Issues Found
1. [Issue description - Severity: High/Medium/Low]

### Next Steps
1. [Action item 1]
2. [Action item 2]
```

---

## 🎉 Ready for Production Checklist

- [ ] All automated tests passing (except expected rate limits)
- [ ] Manual user flow completed successfully  
- [ ] Performance benchmarks met
- [ ] Security validations passed
- [ ] Mobile responsiveness confirmed
- [ ] Error handling working properly
- [ ] Real-time updates functioning
- [ ] Export functionality operational

---

*Keep this reference handy for quick testing validation!*
