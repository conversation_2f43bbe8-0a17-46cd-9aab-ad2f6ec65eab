
<!DOCTYPE html>
<html>
<head>
    <title>I2D-Convert Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .suite { margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background: #e9e9e9; padding: 10px; font-weight: bold; }
        .test-result { padding: 10px; border-bottom: 1px solid #eee; }
        .pass { color: green; }
        .fail { color: red; }
        .skip { color: orange; }
    </style>
</head>
<body>
    <div class="header">
        <h1>I2D-Convert Test Report</h1>
        <p><strong>Timestamp:</strong> 2025-08-01T14:28:49.606Z</p>
        <p><strong>Duration:</strong> 4.32s</p>
        <p><strong>Tests:</strong> 9 | 
           <span class="pass">Passed: 3</span> | 
           <span class="fail">Failed: 6</span></p>
    </div>
    
    
        <div class="suite">
            <div class="suite-header">Environment Validation</div>
            
                <div class="test-result pass">
                    <strong>Node.js Version Check:</strong> Node.js v22.14.0 ✅ (43ms)
                </div>
            
                <div class="test-result pass">
                    <strong>Docker Availability:</strong> Docker available ✅ (70ms)
                </div>
            
                <div class="test-result fail">
                    <strong>Environment Variables:</strong> Missing: DATABASE_URL, REDIS_URL, JWT_SECRET ❌ (0ms)
                </div>
            
        </div>
    
        <div class="suite">
            <div class="suite-header">Service Health Checks</div>
            
                <div class="test-result fail">
                    <strong>Service Startup:</strong> Service startup failed: Error: Command failed: npm run dev:services
npm error Missing script: "dev:services"
npm error
npm error Did you mean this?
npm error   npm run dev:server # run the "dev:server" package script
npm error
npm error To see a list of scripts, run:
npm error   npm run
npm error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-08-01T14_28_45_628Z-debug-0.log
 ❌ (307ms)
                </div>
            
                <div class="test-result fail">
                    <strong>Environment Validation:</strong> Validation error: Error: Command failed: npm run validate-env
 ❌ (722ms)
                </div>
            
        </div>
    
        <div class="suite">
            <div class="suite-header">Smoke Tests</div>
            
                <div class="test-result fail">
                    <strong>Smoke Tests:</strong> Test execution failed: Error: Command failed: npm run test:smoke
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[31mNo test files found, exiting with code 1
[39m
[2mfilter: [22m[33mtests/smoke/[39m
[2minclude: [22m[33m**/*.{test,spec}.?(c|m)[jt]s?(x)[39m
[2mexclude:  [22m[33m**/node_modules/**[2m, [22m**/dist/**[2m, [22m**/cypress/**[2m, [22m**/.{idea,git,cache,output,temp}/**[2m, [22m**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build,eslint,prettier}.config.*[39m

(node:62476) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///C:/Users/<USER>/projects/I2D-Convert/postcss.config.js?t=1754058527189 is not specified and it doesn't parse as CommonJS.
Reparsing as ES module because module syntax was detected. This incurs a performance overhead.
To eliminate this warning, add "type": "module" to C:\Users\<USER>\projects\I2D-Convert\package.json.
(Use `node --trace-warnings ...` to show where the warning was created)
 ❌ (1154ms)
                </div>
            
        </div>
    
        <div class="suite">
            <div class="suite-header">End-to-End Tests</div>
            
                <div class="test-result pass">
                    <strong>End-to-End Tests:</strong> Test failed as expected (rate limiting) ✅ (1025ms)
                </div>
            
        </div>
    
        <div class="suite">
            <div class="suite-header">Performance Tests</div>
            
                <div class="test-result fail">
                    <strong>WebSocket Performance:</strong> Performance test failed: Error: Command failed: npm run test:performance
[33mThe CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
[31mNo test files found, exiting with code 1
[39m
[2mfilter: [22m[33mtests/performance/[39m
[2minclude: [22m[33m**/*.{test,spec}.?(c|m)[jt]s?(x)[39m
[2mexclude:  [22m[33m**/node_modules/**[2m, [22m**/dist/**[2m, [22m**/cypress/**[2m, [22m**/.{idea,git,cache,output,temp}/**[2m, [22m**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build,eslint,prettier}.config.*[39m

(node:64700) [MODULE_TYPELESS_PACKAGE_JSON] Warning: Module type of file:///C:/Users/<USER>/projects/I2D-Convert/postcss.config.js?t=1754058529277 is not specified and it doesn't parse as CommonJS.
Reparsing as ES module because module syntax was detected. This incurs a performance overhead.
To eliminate this warning, add "type": "module" to C:\Users\<USER>\projects\I2D-Convert\package.json.
(Use `node --trace-warnings ...` to show where the warning was created)
 ❌ (952ms)
                </div>
            
        </div>
    
        <div class="suite">
            <div class="suite-header">Security Tests</div>
            
                <div class="test-result fail">
                    <strong>Rate Limiting:</strong> Rate limiting test failed: TypeError: fetch failed ❌ (41ms)
                </div>
            
        </div>
    
</body>
</html>