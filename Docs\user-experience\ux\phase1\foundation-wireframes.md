# Phase 1: Foundation Wireframes

**Document Version:** 1.0  
**Date:** 31 July 2025  
**Project:** I2D-Convert Foundation Phase UX/UI Design  
**Maintainer:** Kanousei Technology LLC

---

## 1. Landing Page Design

### 1.1 Hero Section
```
┌─────────────────────────────────────────────────────────────┐
│ [Logo] I2D-Convert    [Features] [Pricing] [Docs] [Login]  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│    Transform UI Screenshots to Production Code             │
│         Convert designs to React, Vue, CSS instantly       │
│                                                             │
│    [Upload Image to Try Free]  [Watch Demo Video]         │
│                                                             │
│    ┌─────────────────┐  ┌─────────────────┐               │
│    │   Before:       │  │   After:        │               │
│    │ [UI Screenshot] │→ │ [Generated Code]│               │
│    └─────────────────┘  └─────────────────┘               │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Key Elements**:
- Clear value proposition in headline
- Immediate trial option without signup
- Visual before/after demonstration
- Social proof indicators (user count, testimonials)

### 1.2 Features Section
```
┌─────────────────────────────────────────────────────────────┐
│                    How It Works                             │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 1. Upload   │  │ 2. AI Magic │  │ 3. Export   │        │
│  │ [Icon]      │  │ [Icon]      │  │ [Icon]      │        │
│  │ Drop your   │  │ Our AI      │  │ Get clean   │        │
│  │ UI image    │  │ analyzes &  │  │ production  │        │
│  │             │  │ converts    │  │ ready code  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│                    Key Features                             │
│                                                             │
│  ✓ 90%+ Accuracy    ✓ Multiple Formats    ✓ Design Tokens │
│  ✓ RTL Support      ✓ Component Library   ✓ Team Sharing  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 Pricing Preview
```
┌─────────────────────────────────────────────────────────────┐
│                      Pricing                                │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Free     │  │   Basic     │  │    Pro      │        │
│  │             │  │             │  │             │        │
│  │  5 tokens   │  │ 50 tokens   │  │ 500 tokens  │        │
│  │  Basic      │  │ Standard    │  │ Advanced    │        │
│  │  features   │  │ processing  │  │ features    │        │
│  │             │  │             │  │             │        │
│  │ [Try Free]  │  │ [$9/month]  │  │ [$29/month] │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Authentication Modals

### 2.1 Login Modal
```
┌─────────────────────────────────────────┐
│  ×                    Login             │
├─────────────────────────────────────────┤
│                                         │
│  Welcome back to I2D-Convert            │
│                                         │
│  Email: [________________]              │
│  Password: [________________]           │
│                                         │
│  □ Remember me    [Forgot password?]    │
│                                         │
│  [Login]                                │
│                                         │
│  ─────────── or ───────────             │
│                                         │
│  [Continue with Google]                 │
│  [Continue with GitHub]                 │
│                                         │
│  Don't have an account? [Sign up]       │
│                                         │
└─────────────────────────────────────────┘
```

### 2.2 Signup Modal
```
┌─────────────────────────────────────────┐
│  ×                   Sign Up            │
├─────────────────────────────────────────┤
│                                         │
│  Create your I2D-Convert account        │
│                                         │
│  Full Name: [________________]          │
│  Email: [________________]              │
│  Password: [________________]           │
│  Confirm: [________________]            │
│                                         │
│  □ I agree to Terms & Privacy Policy    │
│  □ Send me product updates              │
│                                         │
│  [Create Account]                       │
│                                         │
│  ─────────── or ───────────             │
│                                         │
│  [Continue with Google]                 │
│  [Continue with GitHub]                 │
│                                         │
│  Already have an account? [Login]       │
│                                         │
└─────────────────────────────────────────┘
```

---

## 3. Dashboard Layout

### 3.1 Main Dashboard
```
┌─────────────────────────────────────────────────────────────┐
│ [Logo] I2D-Convert    [Search] [Notifications] [Profile ▼] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Welcome back, Sarah!                    Tokens: 45/50     │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐                 │
│  │ Quick Actions   │  │ Recent Activity │                 │
│  │                 │  │                 │                 │
│  │ [New Project]   │  │ • Project A     │                 │
│  │ [Upload Image]  │  │   completed     │                 │
│  │ [Browse Temp.]  │  │ • Image B       │                 │
│  │                 │  │   processing    │                 │
│  └─────────────────┘  └─────────────────┘                 │
│                                                             │
│  Your Projects                          [Grid] [List] [+]  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Project A   │  │ Project B   │  │ Project C   │        │
│  │ [Thumbnail] │  │ [Thumbnail] │  │ [Thumbnail] │        │
│  │ 5 images    │  │ 2 images    │  │ 8 images    │        │
│  │ Updated 2h  │  │ Updated 1d  │  │ Updated 3d  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Project Detail View
```
┌─────────────────────────────────────────────────────────────┐
│ [← Back] Project A                      [Share] [Settings]  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Project Overview                                           │
│  Created: Jan 15, 2025    Images: 5    Status: Active      │
│                                                             │
│  Images in this project                 [Upload] [Import]  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ login.png   │  │ dashboard.  │  │ profile.png │        │
│  │ [Thumbnail] │  │ [Thumbnail] │  │ [Thumbnail] │        │
│  │ ✓ Processed │  │ ⏳ Processing│  │ ❌ Failed   │        │
│  │ [Open]      │  │ [View]      │  │ [Retry]     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐                         │
│  │ settings.png│  │ [+ Add New] │                         │
│  │ [Thumbnail] │  │             │                         │
│  │ ✓ Processed │  │             │                         │
│  │ [Open]      │  │             │                         │
│  └─────────────┘  └─────────────┘                         │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 4. Upload Interface

### 4.1 Upload Area
```
┌─────────────────────────────────────────────────────────────┐
│ Upload Images                                    [× Close]  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                                                     │   │
│  │              📁 Drag & Drop Images Here             │   │
│  │                                                     │   │
│  │                      or                             │   │
│  │                                                     │   │
│  │                [Browse Files]                       │   │
│  │                                                     │   │
│  │  Supported: PNG, JPG (max 10MB each)              │   │
│  │                                                     │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Upload Options:                                            │
│  Project: [Select Project ▼]                               │
│  □ Manual mode (skip AI processing)                        │
│  □ High accuracy mode (+2 tokens per image)                │
│                                                             │
│  [Cancel]                              [Upload & Process]  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Upload Progress
```
┌─────────────────────────────────────────────────────────────┐
│ Processing Images                                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ login.png                                           │   │
│  │ ████████████████████████████████████████ 100%      │   │
│  │ ✓ Upload complete • Processing...                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ dashboard.png                                       │   │
│  │ ████████████████████████████████████████ 100%      │   │
│  │ ✓ Upload complete • Queued for processing          │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ profile.png                                         │   │
│  │ ████████████████████████████████████████ 100%      │   │
│  │ ✓ Upload complete • Queued for processing          │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Overall Progress: 1 of 3 complete                         │
│  Estimated time remaining: 2 minutes                       │
│                                                             │
│  [Cancel Remaining]                    [Continue in BG]    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 5. Basic Admin Interface

### 5.1 Admin Dashboard
```
┌─────────────────────────────────────────────────────────────┐
│ [Logo] I2D-Convert Admin    [Notifications] [Admin ▼]      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  System Overview                                            │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Active Users│  │ Processing  │  │ System      │        │
│  │    1,247    │  │ Queue: 23   │  │ Health: ✓   │        │
│  │ (+12% week) │  │ Avg: 8.5s   │  │ Uptime: 99.9│        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Revenue     │  │ Token Usage │  │ Support     │        │
│  │   $12,450   │  │   45,230    │  │ Tickets: 8  │        │
│  │ (+8% month) │  │ (+15% week) │  │ Avg: 2.1h   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
│  Recent Activity                                            │
│  • User <EMAIL> upgraded to Pro                  │
│  • Processing queue cleared after 15min spike              │
│  • New support ticket: "Export format issue"              │
│  • System backup completed successfully                    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 User Management
```
┌─────────────────────────────────────────────────────────────┐
│ Users                                          [+ Add User] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  [Search users...] [Filter ▼] [Export]                     │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐
│  │ Name          Email              Plan    Status  Actions│
│  ├─────────────────────────────────────────────────────────┤
│  │ <NAME_EMAIL>  Pro     Active  [Edit] │
│  │ Mike Chen     <EMAIL>    Basic   Active  [Edit] │
│  │ Alex Rivera   <EMAIL>    Free    Active  [Edit] │
│  │ John Smith    <EMAIL>      Pro     Suspended [Edit]│
│  │ ...                                                     │
│  └─────────────────────────────────────────────────────────┘
│                                                             │
│  Showing 1-20 of 1,247 users                    [1][2][3]  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 6. Responsive Considerations

### 6.1 Mobile Dashboard
```
┌─────────────────────┐
│ ☰ I2D-Convert   👤  │
├─────────────────────┤
│                     │
│ Welcome back!       │
│ Tokens: 45/50       │
│                     │
│ [New Project]       │
│ [Upload Image]      │
│                     │
│ Recent Projects     │
│ ┌─────────────────┐ │
│ │ Project A       │ │
│ │ 5 images        │ │
│ │ Updated 2h ago  │ │
│ └─────────────────┘ │
│ ┌─────────────────┐ │
│ │ Project B       │ │
│ │ 2 images        │ │
│ │ Updated 1d ago  │ │
│ └─────────────────┘ │
│                     │
└─────────────────────┘
```

### 6.2 Tablet Upload Interface
```
┌─────────────────────────────────────────┐
│ Upload Images                    [×]    │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │                                     │ │
│ │        📁 Tap to Upload             │ │
│ │                                     │ │
│ │     or drag images here             │ │
│ │                                     │ │
│ │   PNG, JPG (max 10MB each)         │ │
│ │                                     │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ Project: [Select ▼]                    │
│ □ Manual mode                          │
│ □ High accuracy                        │
│                                         │
│ [Cancel]           [Upload]            │
│                                         │
└─────────────────────────────────────────┘
```

---

*These wireframes provide the foundation for Phase 1 development, focusing on core user flows and essential functionality while maintaining simplicity and usability.*
