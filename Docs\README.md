# I2D-Convert Documentation

This directory contains all project documentation organized into logical folders.

## 📁 Directory Structure

### `/development/`
Development-related documents and handover information:
- `HANDOVER.md` - Phase 1 to Phase 2 handover document
- `TASK_DETAILS.md` - Detailed implementation guide for Phase 1 tasks
- `handover-phase2.md` - Phase 2 handover and task breakdown
- `development-summary.md` - Development progress summary
- `implementation-status.md` - Current implementation status

### `/planning/`
Project planning, requirements, and user stories:
- `initial.md` - Initial project requirements and vision
- `plan.md` - Comprehensive project plan and roadmap
- `tasks.md` - Task breakdown and management
- `stories/` - User stories organized by phase
  - `phase1-foundation.md` - Foundation phase stories
  - `phase2-processing.md` - Core processing phase stories
  - `phase3-experience.md` - User experience phase stories
  - `phase4-production.md` - Production readiness stories
  - `ux-stories.md` - UX-specific user stories

### `/specifications/`
Technical specifications and system design:
- `layout.md` - UI layout specification and wireframes
- `error-handling-system.md` - Error handling system design
- `qc_checklist.md` - Quality control checklist
- `specs/` - Detailed technical specifications
  - `image-ingestion-pipeline-spec.md` - Image processing pipeline spec
- `testing/` - Testing documentation and guides
  - `README.md` - Testing overview
  - `comprehensive-user-testing-guide.md` - Complete testing guide
  - `end-to-end-manual-test-guide.md` - Manual testing procedures
  - `quick-test-reference.md` - Quick testing reference
  - `test-dashboard.html` - Testing dashboard
  - `test-execution-guide.md` - Test execution procedures

### `/user-experience/`
UX design, user journeys, and interface specifications:
- `ux/` - Complete UX documentation
  - `README.md` - UX overview
  - `journeys/` - User journey maps
  - `phase1/` - Phase 1 UX specifications
  - `phase2/` - Phase 2 UX specifications
  - `phase3/` - Phase 3 UX specifications
  - `strategy/` - UX strategy documents

### `/archive/`
Archived documents and development artifacts:
- Historical versions of documents
- Development test files and scripts
- Temporary files and artifacts
- Previous versions of plans and specifications

## 🔄 Document Lifecycle

1. **Active Development**: Documents are created and maintained in their respective folders
2. **Version Control**: Major changes create new versions while preserving history
3. **Archival**: Outdated documents are moved to `/archive/` with clear naming
4. **Reference**: Archived documents remain available for historical reference

## 📝 Contributing to Documentation

When adding or updating documentation:

1. **Choose the Right Folder**: Place documents in the most appropriate category
2. **Use Clear Naming**: Use descriptive filenames with consistent conventions
3. **Update This README**: Add new documents to the directory structure above
4. **Link Related Docs**: Cross-reference related documents where helpful
5. **Archive Old Versions**: Move superseded documents to `/archive/`

## 🎯 Quick Navigation

- **Need to understand the project?** Start with `/planning/initial.md`
- **Working on development?** Check `/development/` for current status
- **Looking for technical specs?** Browse `/specifications/`
- **Designing user experience?** Explore `/user-experience/`
- **Need testing info?** Visit `/specifications/testing/`
- **Looking for old files?** Check `/archive/`

---

*This documentation structure supports the I2D-Convert project's development from initial planning through production deployment.*
