# Project: Image‑to‑Design‑System Converter

**Document Version:** 1.1 (Updated with UX/UI Integration)
**Date:** 31 July 2025
**Maintainer:** Kanousei Technology LLC

> **📋 UX/UI Update**: This document has been updated to incorporate comprehensive UX/UI planning.
> See `Docs/ux/` directory for detailed user journeys, wireframes, and implementation specifications.

---

## Table of Contents

1. Executive Summary
2. Vision & Goals
3. Product Requirements Document (PRD)

   1. Key Features by Release
   2. Functional Requirements
   3. Non‑Functional Requirements
   4. Data & Model Architecture
   5. Pricing & Tokenisation
4. UX /UI Specification

   1. Global Information Architecture
   2. Tabbed Workflow (Wireframe ➜ Components ➜ Style ➜ Review)
   3. Contextual Side‑Menu Logic
   4. Evaluate & Gamification Layer
   5. Accessibility & RTL Support
5. Roadmap & Milestones
6. Open Questions / Risks

---

## 1. Executive Summary

A web application that ingests static UI images or screenshots and outputs production‑ready design‑system assets.
It uses a Mixture‑of‑Experts (MoE) vision pipeline to segment, classify and map regions to design‑system components, supports RTL/Arabic conversion, and provides a four‑step visual workflow (Wireframe, Components, Style, Review).
Tokens meter AI compute; three paid tiers plus one free tier gate advanced exports and MCP queries.

---

## 2. Vision & Goals

| Objective                   | KPI                               | Target (12 months)         |
| --------------------------- | --------------------------------- | -------------------------- |
| **90 % one‑click accuracy** | Correct segmentation & tag match  |  ≥ 90 % across beta cohort |
| **Time‑to‑prototype**       | From upload → CSS/JSX export      |  ≤ 3 min avg               |
| **User retention**          | MAU / WAU for Basic & Enthusiast  |  ≥ 40 %                    |
| **Revenue**                 | MRR from paid plans & token packs |  ≥ 20 k USD                |

---

## 3. Product Requirements Document (PRD)

### 3.1 Key Features by Release

| Release             | Core Scope                                                                                          |
| ------------------- | --------------------------------------------------------------------------------------------------- |
| **Beta (Q3 2025)**  | Image upload ➜ MoE segmentation ➜ JSON export · Unlimited features · Token accounting soft‑enforced |
| **GA v1 (Q4 2025)** | Plan gating (Free/Basic/Enthusiast/Pro) · Tabbed UI · Arabic Con‑Version · MCP queries              |
| **v1.5 (Q1 2026)**  | Collaboration, live comments, version history                                                       |
| **v2.0 (H2 2026)**  | Live code preview, CI/CD integrations, public plugin marketplace                                    |

### 3.2 Functional Requirements

1. **Image Ingestion** – accept PNG/JPG; detect `_ui` filename for manual‑only mode.
2. **MoE Pipeline** – Structural, Feature, Functional, Layout experts; confidence aggregation.
3. **Auto‑Mode** – Text prompt → placeholder components & layout.
4. **Tabbed Editor** – Wireframe / Components / Style / Review; edits persist across tabs.
5. **Override Protection** – Manual changes flagged `isOverridden`; AI actions prompt before overwrite.
6. **Arabic RTL Conversion** – Flip layout, translate text, swap to RTL components.
7. **Token Engine** – Deduct per compute op; block when balance = 0.
8. **Export** – JSON (all), JSX/CSS (Basic+), Animations (Enthusiast/Pro).
9. **Review Audit** – Lint for accessibility, semantic structure, grid alignment, responsive breakpoints.
10. **Evaluate Layer** – Rating, feedback, points, badges, leaderboard API.

### 3.3 Non‑Functional Requirements

* **Latency**: ≤ 10 s for full MoE run on ≤ 1080 p screenshot.
* **Uptime**: 99.5 % (GA).
* **Security**: OWASP Top‑10 compliance, encrypted uploads, GDPR ready.
* **Scalability**: Horizontal autoscale for model servers.

### 3.4 Data & Model Architecture

```
Client ► API ▲──────────────► Token Service
        │                   │
        │         ┌────────► MoE Ensemble (GPU)
        ▼         │           • Structural CNN
Upload Bucket ────┘           • Feature CNN
                               • Functional LLM (4o‑mini)
                               • Layout GNN
```

* **Base Dataset**: RICO (\~72 k Android screens) + WebUI + in‑house Arabic dataset.
* **Active Learning**: User overrides labelled and added to fine‑tune queue monthly.

### 3.5 Pricing & Tokenisation

| Plan       | Price | Monthly Tokens | Pages/Project | Exclusive Features                |
| ---------- | ----- | -------------- | ------------- | --------------------------------- |
| Free       | \$0   | 5              | 3             | –                                 |
| Basic      | \$5   | 50             | 5             | –                                 |
| Enthusiast | \$15  | 200            | 10            | RTL/Arabic, MCP, Animations       |
| Pro        | \$25  | 500            | ∞             | Same as Enthusiast, higher tokens |

Token costs: Segmentation = 1, Classification = 1, Auto‑layout = 1, Export = 2, Animation = 3, MCP query = 2.

---

## 4. UX /UI Specification

### 4.1 Information Architecture

```
┌──Top Nav─────────────────────────┐
│ Logo | [Wireframe] [Components] [Style] [Review] | Token Meter │
└──────────────────────────────────┘
┌─Canvas─────────────┬─Contextual Side Menu────────────┐
│ • Main image / box │ • Tab‑specific panels           │
│ • Split/PIP toggle │ • Atom/Molecule/O toggle        │
│ • Issue markers    │ • Breadcrumb widget             │
└────────────────────┴─────────────────────────────────┘
```

### 4.2 Tab Details

* **Wireframe Tab**: Grey overlay, draw/resize boxes, assign layout tags, confidence badges.
* **Components Tab**: B/W placeholders, library picker, original‑vs‑component split view.
* **Style Tab**: Full colour, token inspector, live theme edits.
* **Review Tab**: Checklist, issue list pane, inline warnings, export report.

### 4.3 Contextual Side‑Menu Logic

| Tab        | Visible Panels                                       |
| ---------- | ---------------------------------------------------- |
| Wireframe  | Box‑tools · Tag picker · Layout tags                 |
| Components | Library browser · Atom/Molecule toggles              |
| Style      | Colour palette · Typography · Spacing · Token viewer |
| Review     | Issue filter · Report export                         |

### 4.4 Evaluate & Gamification

* **Rating Controls**: 👍/👎 on element hover; 1‑5★ on page header.
* **Points**: +2 per element rating, +5 per page rating, +10 per comment.
* **Badges**: Reviewer I (50 pts), Reviewer II (200 pts), Critic (500 pts).
* **Leaderboard**: Org‑wide & global toggle; resets monthly.

### 4.5 Accessibility & RTL

* WCAG 2.2 AA colour checks, text‑size warnings.
* RTL toggle simulates mirrored layout in canvas.

---

## 5. Roadmap & Milestones

| Quarter     | Milestone     | Deliverables                              |
| ----------- | ------------- | ----------------------------------------- |
| **Q3 2025** | Private Beta  | Upload → JSON export · Unlimited usage    |
| **Q4 2025** | GA v1         | Plan gating · Arabic· MCP                 |
| **Q1 2026** | Collaboration | Live cursors · Comments · Version history |
| **Q2 2026** | Developer DX  | Live code preview · CI/CD plugins         |
| **H2 2026** | Marketplace   | Public plugin API · Revenue share         |

---

## 6. Open Questions / Risks

1. **Arabic dataset sufficiency** – may need 5 k+ labelled screens for robust RTL mapping. There should be datasets we can adopt.
2. **Token economics tuning** – adjust prices once real usage data arrives.
3. **Latency** – ensure <10 s SLA at 90th percentile under load.
4. **Gamification abuse** – guard against spam ratings via rate‑limiting.

---