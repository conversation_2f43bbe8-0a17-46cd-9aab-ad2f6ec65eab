const fs = require('fs')
const path = require('path')

// Create a simple test script to test the upload endpoint
const testUpload = async () => {
  try {
    // Create a simple test file (1x1 PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ])

    // Write test image to file
    fs.writeFileSync('test-image.png', testImageBuffer)
    console.log('✓ Created test image file')

    // Test with curl command
    console.log('Testing upload endpoint...')
    console.log('Run this command to test:')
    console.log('curl -X POST -F "file=@test-image.png" http://localhost:3001/api/images/upload')

  } catch (error) {
    console.error('Error:', error)
  }
}

testUpload()
