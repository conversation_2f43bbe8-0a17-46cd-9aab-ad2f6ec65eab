const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001/api/v1';

async function testAuthenticationFlow() {
  console.log('🔐 Testing Complete Authentication Flow...\n');
  
  const testEmail = `authflow${Date.now()}@example.com`;
  const testPassword = 'testpassword123';
  let authToken = '';
  let refreshToken = '';

  try {
    // Step 1: Test user registration
    console.log('1. Testing user registration...');
    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        name: 'Auth Flow Test User'
      })
    });
    
    const registerData = await registerResponse.json();
    if (registerData.success) {
      authToken = registerData.data.token;
      console.log('✅ User registration successful');
      console.log('   User ID:', registerData.data.user.id);
      console.log('   Email:', registerData.data.user.email);
      console.log('   Token received:', authToken ? 'Yes' : 'No');
    } else {
      throw new Error(`Registration failed: ${registerData.error}`);
    }

    // Step 2: Test token verification
    console.log('\n2. Testing token verification...');
    const verifyResponse = await fetch(`${BASE_URL}/auth/verify`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    const verifyData = await verifyResponse.json();
    if (verifyData.success) {
      console.log('✅ Token verification successful');
      console.log('   Verified user:', verifyData.data.user.email);
      console.log('   User role:', verifyData.data.user.role);
    } else {
      throw new Error(`Token verification failed: ${verifyData.error}`);
    }

    // Step 3: Test user login (simulate logout and login again)
    console.log('\n3. Testing user login...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });
    
    const loginData = await loginResponse.json();
    if (loginData.success) {
      const newToken = loginData.data.token;
      console.log('✅ User login successful');
      console.log('   New token received:', newToken ? 'Yes' : 'No');
      console.log('   Token different from registration:', newToken !== authToken ? 'Yes' : 'No');
      
      // Update token for subsequent tests
      authToken = newToken;
    } else {
      throw new Error(`Login failed: ${loginData.error}`);
    }

    // Step 4: Test protected resource access
    console.log('\n4. Testing protected resource access...');
    const profileResponse = await fetch(`${BASE_URL}/users/me`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    
    const profileData = await profileResponse.json();
    if (profileData.success) {
      console.log('✅ Protected resource access successful');
      console.log('   Profile name:', profileData.data.name);
      console.log('   Profile email:', profileData.data.email);
      console.log('   Token balance:', profileData.data.tokenBalance);
    } else {
      throw new Error(`Protected resource access failed: ${profileData.error}`);
    }

    // Step 5: Test session persistence
    console.log('\n5. Testing session persistence...');
    
    // Make multiple requests to verify session is maintained
    const sessionTests = [
      { endpoint: '/users/me/stats', name: 'User Stats' },
      { endpoint: '/projects', name: 'Projects' },
      { endpoint: '/users/me/activity', name: 'Activity Log' }
    ];

    for (const test of sessionTests) {
      const response = await fetch(`${BASE_URL}${test.endpoint}`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      
      const data = await response.json();
      if (data.success) {
        console.log(`✅ ${test.name} - session maintained`);
      } else {
        console.log(`❌ ${test.name} - session failed:`, data.error);
      }
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Step 6: Test invalid credentials
    console.log('\n6. Testing invalid credentials handling...');
    const invalidLoginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testEmail,
        password: 'wrongpassword'
      })
    });
    
    const invalidLoginData = await invalidLoginResponse.json();
    if (!invalidLoginData.success && invalidLoginResponse.status === 401) {
      console.log('✅ Invalid credentials properly rejected');
    } else {
      console.log('❌ Invalid credentials handling failed');
    }

    // Step 7: Test unauthorized access
    console.log('\n7. Testing unauthorized access protection...');
    const unauthorizedResponse = await fetch(`${BASE_URL}/users/me`, {
      headers: { 'Authorization': 'Bearer invalid-token-here' }
    });
    
    if (unauthorizedResponse.status === 401) {
      console.log('✅ Unauthorized access properly blocked');
    } else {
      console.log('❌ Unauthorized access protection failed');
    }

    // Step 8: Test user preferences update (authenticated operation)
    console.log('\n8. Testing authenticated operations...');
    const updatePrefsResponse = await fetch(`${BASE_URL}/users/me/preferences`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        theme: 'dark',
        language: 'en',
        notifications: {
          email: true,
          push: false
        }
      })
    });
    
    const updatePrefsData = await updatePrefsResponse.json();
    if (updatePrefsData.success) {
      console.log('✅ Authenticated operation successful');
      console.log('   Preferences updated');
    } else {
      console.log('❌ Authenticated operation failed:', updatePrefsData.error);
    }

    // Step 9: Test rate limiting (if implemented)
    console.log('\n9. Testing rate limiting...');
    let rateLimitHit = false;
    
    // Make rapid requests to test rate limiting
    for (let i = 0; i < 20; i++) {
      const response = await fetch(`${BASE_URL}/auth/verify`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      
      if (response.status === 429) {
        rateLimitHit = true;
        console.log('✅ Rate limiting is active');
        break;
      }
    }
    
    if (!rateLimitHit) {
      console.log('ℹ️ Rate limiting not triggered (may be configured for higher limits)');
    }

    console.log('\n🎉 Authentication flow testing completed successfully!');
    console.log('\n📊 Authentication Summary:');
    console.log('   ✅ User registration working');
    console.log('   ✅ Token generation and verification');
    console.log('   ✅ User login functional');
    console.log('   ✅ Protected routes secured');
    console.log('   ✅ Session persistence maintained');
    console.log('   ✅ Invalid credentials rejected');
    console.log('   ✅ Unauthorized access blocked');
    console.log('   ✅ Authenticated operations working');
    console.log('   ✅ Security measures in place');

  } catch (error) {
    console.error('❌ Authentication flow test failed:', error.message);
  }
}

testAuthenticationFlow();
