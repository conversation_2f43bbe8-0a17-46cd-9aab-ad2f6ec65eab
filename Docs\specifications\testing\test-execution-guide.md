# Test Execution Guide

This guide provides step-by-step instructions for running the complete end-to-end testing suite for I2D-Convert.

## Prerequisites

### 1. System Requirements
- Node.js 18+ installed
- Redis server available
- At least 2GB free RAM
- 1GB free disk space

### 2. Environment Setup

```bash
# Clone and setup project
git clone <repository-url>
cd I2D-Convert
npm install

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration

# Initialize database
npx prisma generate
npx prisma db push
```

### 3. Service Dependencies

Ensure these services are available:
- **Redis**: `redis-server` (port 6379)
- **Database**: SQLite or PostgreSQL
- **Storage**: Local filesystem or S3-compatible storage

## Quick Test Execution

### 1. Smoke Tests (2 minutes)

Quick verification that basic functionality works:

```bash
# Start services
npm run dev:server &
npm run dev:worker &

# Wait 10 seconds for services to start
sleep 10

# Run smoke tests
npm run test:smoke
```

**Expected Output:**
```
✅ Server Health - should respond to health check
✅ WebSocket Server - should accept WebSocket connections
✅ Database Connectivity - should connect to database through API
✅ Basic Integration - should handle a complete basic flow
```

### 2. Environment Validation (1 minute)

Verify all services are properly configured:

```bash
npm run validate-env
```

**Expected Output:**
```
✅ Backend API Health (150ms)
✅ WebSocket Server Handshake (89ms)
✅ Database Connectivity (234ms)
✅ Static File Serving (45ms)

📊 Summary: 4 passed, 0 failed
```

### 3. End-to-End Tests (5-10 minutes)

Test the complete workflow:

```bash
npm run test:e2e
```

**Expected Output:**
```
✅ Authentication Flow - should authenticate user successfully
✅ WebSocket Connection - should connect to WebSocket server
✅ Image Upload Flow - should upload image successfully
✅ Complete End-to-End Flow - should process image with real-time updates
✅ Processing Jobs API - should fetch processing jobs
```

### 4. Performance Tests (10-15 minutes)

Test system performance under load:

```bash
npm run test:performance
```

**Expected Output:**
```
✅ Concurrent Upload Load Test - should handle multiple concurrent uploads
✅ WebSocket Connection Load Test - should handle multiple concurrent WebSocket connections
✅ Memory Usage Test - should maintain reasonable memory usage under load
✅ Performance Metrics Summary - should generate performance report
```

## Comprehensive Test Suite

### Full Automated Testing (20-30 minutes)

Run the complete automated test suite:

```bash
npm run test:e2e-full
```

This script will:
1. ✅ Check environment prerequisites
2. ✅ Start all required services
3. ✅ Wait for services to be ready
4. ✅ Run smoke tests
5. ✅ Run end-to-end tests
6. ✅ Run performance tests
7. ✅ Generate comprehensive reports
8. ✅ Clean up resources

**Expected Timeline:**
- Environment setup: 2-3 minutes
- Service startup: 1-2 minutes
- Test execution: 15-20 minutes
- Report generation: 1 minute
- Cleanup: 30 seconds

## Manual Testing

### 1. User Interface Testing (15-20 minutes)

Follow the manual testing guide:

```bash
# Start all services
npm run dev:full

# Open browser to http://localhost:3000
# Follow: docs/testing/end-to-end-manual-test-guide.md
```

**Key Test Scenarios:**
- [ ] User registration and login
- [ ] Image upload with drag-and-drop
- [ ] Real-time processing progress
- [ ] Error handling (invalid files, network issues)
- [ ] Multiple concurrent uploads
- [ ] Mobile responsiveness

### 2. API Testing with Postman/Insomnia

Import the API collection and test endpoints:

```bash
# Generate API documentation
npm run docs:api

# Test key endpoints:
# POST /api/v1/auth/register
# POST /api/v1/auth/login
# POST /api/v1/images/upload
# GET /api/v1/processing/jobs
```

## Test Results and Reports

### 1. Console Output

All tests provide detailed console output:
- ✅ Passed tests with timing
- ❌ Failed tests with error details
- 📊 Performance metrics
- 🔧 Debug information

### 2. Generated Reports

Reports are saved to `test-results/`:

```
test-results/
├── e2e-report.html          # Human-readable HTML report
├── e2e-report.json          # Machine-readable JSON results
├── performance-metrics.json # Performance benchmarks
└── coverage/                # Code coverage reports
```

### 3. HTML Report

Open the HTML report in your browser:

```bash
# macOS
open test-results/e2e-report.html

# Linux
xdg-open test-results/e2e-report.html

# Windows
start test-results/e2e-report.html
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Services Not Starting

**Problem:** Tests fail with connection errors

**Solution:**
```bash
# Check if ports are in use
netstat -an | grep 3001
netstat -an | grep 6379

# Kill existing processes
pkill -f "node.*server"
pkill -f "redis-server"

# Restart services
npm run dev:server &
npm run dev:worker &
redis-server &
```

#### 2. Database Issues

**Problem:** Database connection errors

**Solution:**
```bash
# Reset database
npx prisma db push --force-reset

# Check database file permissions
ls -la prisma/dev.db

# Regenerate Prisma client
npx prisma generate
```

#### 3. WebSocket Connection Failures

**Problem:** WebSocket tests timeout

**Solution:**
```bash
# Check WebSocket endpoint manually
curl -I http://localhost:3001/ws/socket.io/

# Verify CORS settings in server configuration
# Check firewall/antivirus blocking connections
```

#### 4. Memory Issues

**Problem:** Tests fail due to memory constraints

**Solution:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Run tests with memory monitoring
npm run test:performance -- --reporter=verbose
```

#### 5. File Upload Issues

**Problem:** Image upload tests fail

**Solution:**
```bash
# Check file permissions
ls -la tests/e2e/test-image.png

# Verify storage configuration
# Check available disk space
df -h
```

### Debug Mode

Run tests with detailed debugging:

```bash
# Enable all debug output
DEBUG=* npm run test:e2e

# Enable specific debug categories
DEBUG=socket.io:* npm run test:e2e
DEBUG=vitest:* npm run test:performance

# Run with verbose output
npm run test:e2e -- --reporter=verbose --bail=1
```

### Performance Monitoring

Monitor system resources during testing:

```bash
# Monitor memory usage
watch -n 1 'ps aux | grep node'

# Monitor network connections
watch -n 1 'netstat -an | grep 3001'

# Monitor Redis
redis-cli monitor

# Monitor disk I/O
iostat -x 1
```

## Continuous Integration

### GitHub Actions Setup

Add to `.github/workflows/e2e-tests.yml`:

```yaml
name: End-to-End Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup database
        run: |
          npx prisma generate
          npx prisma db push
      
      - name: Run E2E tests
        run: npm run test:e2e-full
        env:
          NODE_ENV: test
          REDIS_URL: redis://localhost:6379
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/
```

### Local Pre-commit Hook

Add to `.husky/pre-commit`:

```bash
#!/bin/sh
npm run test:smoke
npm run validate-env
```

## Success Criteria

### Test Suite Completion

The test suite is considered successful when:

1. **Smoke Tests**: All basic functionality tests pass
2. **Environment Validation**: All services are healthy
3. **End-to-End Tests**: Complete workflow functions correctly
4. **Performance Tests**: System meets performance benchmarks
5. **Manual Tests**: UI/UX functions as expected

### Performance Benchmarks

- **Upload Response Time**: < 5 seconds for 5MB files
- **Processing Time**: < 30 seconds for typical images
- **WebSocket Latency**: < 100ms for progress updates
- **Memory Usage**: < 500MB peak during load tests
- **Success Rate**: > 95% under normal load
- **Concurrent Users**: Support 10+ simultaneous users

### Quality Gates

- **Test Coverage**: > 80% code coverage
- **Error Rate**: < 5% in end-to-end tests
- **Performance Regression**: < 10% slower than baseline
- **Memory Leaks**: No significant memory growth over time

## Next Steps

After successful testing:

1. **Deploy to Staging**: Use test results to validate staging deployment
2. **Production Readiness**: Verify all tests pass in production-like environment
3. **Monitoring Setup**: Implement production monitoring based on test metrics
4. **Documentation**: Update API documentation with test examples
5. **Training**: Share test results with team for deployment confidence
